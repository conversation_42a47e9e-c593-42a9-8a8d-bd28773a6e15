<plugin name='jcaptcha' version='1.2.1' grailsVersion='1.2.1 &gt; *'>
  <author><PERSON></author>
  <authorEmail><EMAIL></authorEmail>
  <title>Grails JCaptcha Plugin</title>
  <description>Makes using <PERSON><PERSON><PERSON>tch<PERSON> within a Grails app simple</description>
  <documentation>http://grails.org/JCaptcha+Plugin</documentation>
  <resources>
    <resource>UrlMappings</resource>
    <resource>org.grails.plugin.jcaptcha.JcaptchaController</resource>
    <resource>org.grails.plugin.jcaptcha.JcaptchaService</resource>
    <resource>org.grails.plugin.jcaptcha.JcaptchaTagLib</resource>
  </resources>
  <dependencies>
    <resolvers>
      <resolver type='grailsPlugins' name='grailsPlugins' />
    </resolvers>
  </dependencies>
  <behavior />
</plugin>