/**
 * Simplest. Plugin. Class. Ever.
 * 
 * <AUTHOR> <<EMAIL>>
 */
class JcaptchaGrailsPlugin {
	def title = "Grails JCaptcha Plugin"
	def description = 'Makes using <PERSON><PERSON><PERSON><PERSON><PERSON> within a Grails app simple'
	def author = "<PERSON>"
	def authorEmail = "<EMAIL>"
	def documentation = "http://grails.org/JCaptcha+Plugin"
	
	def version = "1.2.1"
	def dependsOn = [:]
	
	def doWithSpring = { }
	def doWithApplicationContext = { applicationContext -> }
	def doWithWebDescriptor = { xml -> }
	def doWithDynamicMethods = { ctx -> }
	def onChange = { event -> }
	def onApplicationChange = { event -> }
}
