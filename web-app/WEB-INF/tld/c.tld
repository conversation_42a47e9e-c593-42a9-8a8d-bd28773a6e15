<?xml version="1.0" encoding="UTF-8" ?>

<taglib xmlns="http://java.sun.com/xml/ns/j2ee"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee http://java.sun.com/xml/ns/j2ee/web-jsptaglibrary_2_0.xsd"
    version="2.0">
    
  <description>JSTL 1.1 core library</description>
  <display-name>JSTL core</display-name>
  <tlib-version>1.1</tlib-version>
  <short-name>c</short-name>
  <uri>http://java.sun.com/jsp/jstl/core</uri>

  <validator>
    <description>
        Provides core validation features for JSTL tags.
    </description>
    <validator-class>
        org.apache.taglibs.standard.tlv.JstlCoreTLV
    </validator-class>
  </validator>

  <tag>
    <description>
        Catches any Throwable that occurs in its body and optionally
        exposes it.
    </description>
    <name>catch</name>
    <tag-class>org.apache.taglibs.standard.tag.common.core.CatchTag</tag-class>
    <body-content>JSP</body-content>
    <attribute>
        <description>
Name of the exported scoped variable for the
exception thrown from a nested action. The type of the
scoped variable is the type of the exception thrown.
        </description>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <description>
	Simple conditional tag that establishes a context for
	mutually exclusive conditional operations, marked by
	&lt;when&gt; and &lt;otherwise&gt;
    </description>
    <name>choose</name>
    <tag-class>org.apache.taglibs.standard.tag.common.core.ChooseTag</tag-class>
    <body-content>JSP</body-content>
  </tag>

  <tag>
    <description>
	Simple conditional tag, which evalutes its body if the
	supplied condition is true and optionally exposes a Boolean
	scripting variable representing the evaluation of this condition
    </description>
    <name>if</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.core.IfTag</tag-class>
    <body-content>JSP</body-content>
    <attribute>
        <description>
The test condition that determines whether or
not the body content should be processed.
        </description>
        <name>test</name>
        <required>true</required>
        <rtexprvalue>true</rtexprvalue>
	<type>boolean</type>
    </attribute>
    <attribute>
        <description>
Name of the exported scoped variable for the
resulting value of the test condition. The type
of the scoped variable is Boolean.        
        </description>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Scope for var.
        </description>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <description>
        Retrieves an absolute or relative URL and exposes its contents
        to either the page, a String in 'var', or a Reader in 'varReader'.
    </description>
    <name>import</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.core.ImportTag</tag-class>
    <tei-class>org.apache.taglibs.standard.tei.ImportTEI</tei-class>
    <body-content>JSP</body-content>
    <attribute>
        <description>
The URL of the resource to import.
        </description>
        <name>url</name>
        <required>true</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Name of the exported scoped variable for the
resource's content. The type of the scoped
variable is String.
        </description>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Scope for var.
        </description>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Name of the exported scoped variable for the
resource's content. The type of the scoped
variable is Reader.
        </description>
        <name>varReader</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Name of the context when accessing a relative
URL resource that belongs to a foreign
context.
        </description>
        <name>context</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Character encoding of the content at the input
resource.
        </description>
        <name>charEncoding</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <description>
	The basic iteration tag, accepting many different
        collection types and supporting subsetting and other
        functionality
    </description>
    <name>forEach</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.core.ForEachTag</tag-class>
    <tei-class>org.apache.taglibs.standard.tei.ForEachTEI</tei-class>
    <body-content>JSP</body-content>
    <attribute>
        <description>
Collection of items to iterate over.
        </description>
	<name>items</name>
	<required>false</required>
	<rtexprvalue>true</rtexprvalue>
	<type>java.lang.Object</type>
    </attribute>
    <attribute>
        <description>
If items specified:
Iteration begins at the item located at the
specified index. First item of the collection has
index 0.
If items not specified:
Iteration begins with index set at the value
specified.
        </description>
	<name>begin</name>
	<required>false</required>
	<rtexprvalue>true</rtexprvalue>
	<type>int</type>
    </attribute>
    <attribute>
        <description>
If items specified:
Iteration ends at the item located at the
specified index (inclusive).
If items not specified:
Iteration ends when index reaches the value
specified.
        </description>
	<name>end</name>
	<required>false</required>
	<rtexprvalue>true</rtexprvalue>
	<type>int</type>
    </attribute>
    <attribute>
        <description>
Iteration will only process every step items of
the collection, starting with the first one.
        </description>
	<name>step</name>
	<required>false</required>
	<rtexprvalue>true</rtexprvalue>
	<type>int</type>
    </attribute>
    <attribute>
        <description>
Name of the exported scoped variable for the
current item of the iteration. This scoped
variable has nested visibility. Its type depends
on the object of the underlying collection.
        </description>
	<name>var</name>
	<required>false</required>
	<rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Name of the exported scoped variable for the
status of the iteration. Object exported is of type
javax.servlet.jsp.jstl.core.LoopTagStatus. This scoped variable has nested
visibility.
        </description>
	<name>varStatus</name>
	<required>false</required>
	<rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <description>
	Iterates over tokens, separated by the supplied delimeters
    </description>
    <name>forTokens</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.core.ForTokensTag</tag-class>
    <body-content>JSP</body-content>
    <attribute>
        <description>
String of tokens to iterate over.
        </description>
	<name>items</name>
	<required>true</required>
	<rtexprvalue>true</rtexprvalue>
	<type>java.lang.String</type>
    </attribute>
    <attribute>
        <description>
The set of delimiters (the characters that
separate the tokens in the string).
        </description>
	<name>delims</name>
	<required>true</required>
	<rtexprvalue>true</rtexprvalue>
	<type>java.lang.String</type>
    </attribute>
    <attribute>
        <description>
Iteration begins at the token located at the
specified index. First token has index 0.
        </description>
	<name>begin</name>
	<required>false</required>
	<rtexprvalue>true</rtexprvalue>
	<type>int</type>
    </attribute>
    <attribute>
        <description>
Iteration ends at the token located at the
specified index (inclusive).
        </description>
	<name>end</name>
	<required>false</required>
	<rtexprvalue>true</rtexprvalue>
	<type>int</type>
    </attribute>
    <attribute>
        <description>
Iteration will only process every step tokens
of the string, starting with the first one.
        </description>
	<name>step</name>
	<required>false</required>
	<rtexprvalue>true</rtexprvalue>
	<type>int</type>
    </attribute>
    <attribute>
        <description>
Name of the exported scoped variable for the
current item of the iteration. This scoped
variable has nested visibility.
        </description>
	<name>var</name>
	<required>false</required>
	<rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Name of the exported scoped variable for the
status of the iteration. Object exported is of
type
javax.servlet.jsp.jstl.core.LoopTag
Status. This scoped variable has nested
visibility.
        </description>
	<name>varStatus</name>
	<required>false</required>
	<rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <description>
        Like &lt;%= ... &gt;, but for expressions.
    </description> 
    <name>out</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.core.OutTag</tag-class>
    <body-content>JSP</body-content>
    <attribute>
        <description>
Expression to be evaluated.
        </description>
        <name>value</name>
        <required>true</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Default value if the resulting value is null.
        </description>
        <name>default</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Determines whether characters &lt;,&gt;,&amp;,'," in the
resulting string should be converted to their
corresponding character entity codes. Default value is
true.
        </description>
        <name>escapeXml</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>


  <tag>
    <description>
        Subtag of &lt;choose&gt; that follows &lt;when&gt; tags
        and runs only if all of the prior conditions evaluated to
        'false'
    </description>
    <name>otherwise</name>
    <tag-class>org.apache.taglibs.standard.tag.common.core.OtherwiseTag</tag-class>
    <body-content>JSP</body-content>
  </tag>

  <tag>
    <description>
        Adds a parameter to a containing 'import' tag's URL.
    </description>
    <name>param</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.core.ParamTag</tag-class>
    <body-content>JSP</body-content>
    <attribute>
        <description>
Name of the query string parameter.
        </description>
        <name>name</name>
        <required>true</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Value of the parameter.
        </description>
        <name>value</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <description>
        Redirects to a new URL.
    </description>
    <name>redirect</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.core.RedirectTag</tag-class>
    <body-content>JSP</body-content>
    <attribute>
        <description>
The URL of the resource to redirect to.
        </description>
        <name>url</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Name of the context when redirecting to a relative URL
resource that belongs to a foreign context.
        </description>
        <name>context</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <description>
        Removes a scoped variable (from a particular scope, if specified).
    </description>
    <name>remove</name>
    <tag-class>org.apache.taglibs.standard.tag.common.core.RemoveTag</tag-class>
    <body-content>empty</body-content>
    <attribute>
        <description>
Name of the scoped variable to be removed.
        </description>
        <name>var</name>
        <required>true</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Scope for var.
        </description>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

 <tag>
    <description>
        Sets the result of an expression evaluation in a 'scope'
    </description>
    <name>set</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.core.SetTag</tag-class>
    <body-content>JSP</body-content>
    <attribute>
        <description>
Name of the exported scoped variable to hold the value
specified in the action. The type of the scoped variable is
whatever type the value expression evaluates to.
        </description>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Expression to be evaluated.
        </description>
        <name>value</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Target object whose property will be set. Must evaluate to
a JavaBeans object with setter property property, or to a
java.util.Map object.
        </description>
        <name>target</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Name of the property to be set in the target object.
        </description>
        <name>property</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Scope for var.
        </description>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <description>
        Creates a URL with optional query parameters.
    </description>
    <name>url</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.core.UrlTag</tag-class>
    <body-content>JSP</body-content>
    <attribute>
        <description>
Name of the exported scoped variable for the
processed url. The type of the scoped variable is
String.
        </description>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Scope for var.
        </description>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <description>
URL to be processed.
        </description>
        <name>value</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <description>
Name of the context when specifying a relative URL
resource that belongs to a foreign context.
        </description>
        <name>context</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <description>
	Subtag of &lt;choose&gt; that includes its body if its
	condition evalutes to 'true'
    </description>
    <name>when</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.core.WhenTag</tag-class>
    <body-content>JSP</body-content>
    <attribute>
        <description>
The test condition that determines whether or not the
body content should be processed.
        </description>
        <name>test</name>
        <required>true</required>
        <rtexprvalue>true</rtexprvalue>
	<type>boolean</type>
    </attribute>
  </tag>

</taglib>
