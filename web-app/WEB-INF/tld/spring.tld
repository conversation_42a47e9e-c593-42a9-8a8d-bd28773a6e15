<?xml version="1.0" encoding="ISO-8859-1" ?>
<!DOCTYPE taglib PUBLIC "-//Sun Microsystems, Inc.//DTD JSP Tag Library 1.2//EN" "http://java.sun.com/dtd/web-jsptaglibrary_1_2.dtd">

<taglib>

	<tlib-version>1.1.1</tlib-version>

	<jsp-version>1.2</jsp-version>

	<short-name>Spring</short-name>

	<uri>http://www.springframework.org/tags</uri>

	<description>Spring Framework JSP Tag Library. Authors: <AUTHORS>


	<tag>

		<name>htmlEscape</name>
		<tag-class>org.springframework.web.servlet.tags.HtmlEscapeTag</tag-class>
		<body-content>JSP</body-content>

		<description>
			Sets default HTML escape value for the current page.
			Overrides a "defaultHtmlEscape" context-param in web.xml, if any.
		</description>

		<attribute>
			<name>defaultHtmlEscape</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

	</tag>


	<tag>

		<name>escapeBody</name>
		<tag-class>org.springframework.web.servlet.tags.EscapeBodyTag</tag-class>
		<body-content>JSP</body-content>

		<description>
			Escapes its enclosed body content, applying HTML escaping and/or JavaScript escaping.
			The HTML escaping flag participates in a page-wide or application-wide setting
			(i.e. by HtmlEscapeTag or a "defaultHtmlEscape" context-param in web.xml).
		</description>

		<attribute>
			<name>htmlEscape</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>javaScriptEscape</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

	</tag>


	<tag>

		<name>message</name>
		<tag-class>org.springframework.web.servlet.tags.MessageTag</tag-class>
		<body-content>JSP</body-content>

		<description>
			Retrieves the message with the given code, or text if code isn't resolvable.
			The HTML escaping flag participates in a page-wide or application-wide setting
			(i.e. by HtmlEscapeTag or a "defaultHtmlEscape" context-param in web.xml).
		</description>

		<attribute>
			<name>code</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>arguments</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>text</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>var</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>scope</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>htmlEscape</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>javaScriptEscape</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

	</tag>


	<tag>

		<name>theme</name>
		<tag-class>org.springframework.web.servlet.tags.ThemeTag</tag-class>
		<body-content>JSP</body-content>

		<description>
			Retrieves the theme message with the given code, or text if code isn't resolvable.
			The HTML escaping flag participates in a page-wide or application-wide setting
			(i.e. by HtmlEscapeTag or a "defaultHtmlEscape" context-param in web.xml).
		</description>

		<attribute>
			<name>code</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>arguments</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>text</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>var</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>scope</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>htmlEscape</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>javaScriptEscape</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

	</tag>


	<tag>

		<name>hasBindErrors</name>
		<tag-class>org.springframework.web.servlet.tags.BindErrorsTag</tag-class>
		<body-content>JSP</body-content>

		<description>
			Provides Errors instance in case of bind errors.
			The HTML escaping flag participates in a page-wide or application-wide setting
			(i.e. by HtmlEscapeTag or a "defaultHtmlEscape" context-param in web.xml).
		</description>

		<variable>
			<name-given>errors</name-given>
			<variable-class>org.springframework.validation.Errors</variable-class>
		</variable>

		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>htmlEscape</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

	</tag>


	<tag>

		<name>nestedPath</name>
		<tag-class>org.springframework.web.servlet.tags.NestedPathTag</tag-class>
		<body-content>JSP</body-content>

		<description>
			Sets a nested path to be used by the bind tag's path.
		</description>

		<variable>
			<name-given>nestedPath</name-given>
			<variable-class>java.lang.String</variable-class>
		</variable>

		<attribute>
			<name>path</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

	</tag>


	<tag>

		<name>bind</name>
		<tag-class>org.springframework.web.servlet.tags.BindTag</tag-class>
		<body-content>JSP</body-content>

		<description>
			Provides BindStatus object for the given bind path.
			The HTML escaping flag participates in a page-wide or application-wide setting
			(i.e. by HtmlEscapeTag or a "defaultHtmlEscape" context-param in web.xml).
		</description>

		<variable>
			<name-given>status</name-given>
			<variable-class>org.springframework.web.servlet.support.BindStatus</variable-class>
		</variable>

		<attribute>
			<name>path</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>ignoreNestedPath</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>htmlEscape</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

	</tag>


	<tag>

		<name>transform</name>
		<tag-class>org.springframework.web.servlet.tags.TransformTag</tag-class>
		<body-content>JSP</body-content>

		<description>
			Provides transformation of variables to Strings, using an appropriate
			custom PropertyEditor from BindTag (can only be used inside BindTag).
			The HTML escaping flag participates in a page-wide or application-wide setting
			(i.e. by HtmlEscapeTag or a "defaultHtmlEscape" context-param in web.xml).
		</description>

		<attribute>
			<name>value</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>var</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>scope</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

		<attribute>
			<name>htmlEscape</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
		</attribute>

	</tag>

</taglib>
