jdbc.driverClassName = com.mysql.jdbc.Driver
jdbc.username = root
#线上环境
jdbc.url = jdbc:mysql://***************:3306/cnvd_admin?createDatabaseIfNotExist=true&useUnicode=true&characterEncoding=utf8
jdbc.password = CNVDtyu!@#789QAZbnmESZ
#测试环境
#jdbc.url = ****************************************************************************************************************
#jdbc.password = #EDC4rfv%TGB^YHN
#本地环境
#jdbc.url = ***********************************************************************************************************
#jdbc.password = root
#测试环境
#jdbc.url = *************************************************************************************************************************
#jdbc.password = r9xh*yH*DcEFQE


filePath.importJsonFilePath = /cnvd/qihoo/cnvd.json
filePath.BSCertemplateFilePath = /cnvd/bsmb.pdf
filePath.BSCertificateFilePath = /cnvd/certificate/
filePath.YCCertemplateFilePath = /cnvd/ycmb.pdf
filePath.YCCertificateFilePath = /cnvd/certificate/
filePath.signaturePath = /cnvd/certificate/
filePath.flawAttFilePath = /cnvd/upload/flaw/
filePath.patchInfoAttFilePath =/cnvd/upload/patch/
filePath.exploitAttFilePath = /cnvd/upload/exploit/
filePath.webinfoAttFilePath = /cnvd/upload/webinfo/
filePath.webinfoContentFilePath = /cnvd/upload/webinfo/
filePath.taskAttFilePath = /cnvd/upload/task/
filePath.flawApplyAttFilePath= /cnvd/upload/flawApply/
filePath.batchFlawFilePath = /cnvd/upload/batchFlaw/
filePath.cerFilePath = /openssl/
filePath.TxtFlawPath = /cnvd/TXT/flaw
filePath.TxtProductPath = /cnvd/TXT/product
filePath.TxtReferencePath = /cnvd/TXT/reference
filePath.TxtDomainNamePath = /cnvd/TXT/domainName
filePath.TxtIpPath = /cnvd/TXT/ip
filePath.TxtIndustryPath = /cnvd/TXT/industry

#mail.smtp=smtp.qq.com
#mail.smtpPort=25
mail.smtp=smtp.exmail.qq.com
mail.smtpPort=465
mail.username=<EMAIL>
mail.password=ysYyrPs@1shcFcjx
mail.from=<EMAIL>
mail.flawSendEmail=<EMAIL>

api.qihoo.send.url=http://loudong.360.cn/api/invul
api.qihoo.starttime=
api.qihoo.completion.data.job.flag=true
api.qihoo.user.id=9395

flaw.url.query.interface=http://**************:7788/redisHandle/getFlawIdByUrl
flaw.url.save.interface=http://**************:7788/redisHandle/setFlawUrl