// locations to search for config files that get merged into the main config
// config files can either be Java properties files or ConfigSlurper scripts

// grails.config.locations = [ "classpath:${appName}-config.properties",
//                             "classpath:${appName}-config.groovy",
//                             "file:${userHome}/.grails/${appName}-config.properties",
//                             "file:${userHome}/.grails/${appName}-config.groovy"]

// if(System.properties["${appName}.config.location"]) {
//    grails.config.locations << "file:" + System.properties["${appName}.config.location"]
// }

grails.project.groupId = appName // change this to alter the default package name and Maven publishing destination
grails.mime.file.extensions = true // enables the parsing of file extensions from URLs into the request format
grails.mime.use.accept.header = false
grails.mime.types = [ html: ['text/html','application/xhtml+xml'],
                      xml: ['text/xml', 'application/xml'],
                      text: 'text/plain',
                      js: 'text/javascript',
                      rss: 'application/rss+xml',
                      atom: 'application/atom+xml',
                      css: 'text/css',
                      csv: 'text/csv',
                      all: '*/*',
                      json: ['application/json','text/json'],
                      form: 'application/x-www-form-urlencoded',
                      multipartForm: 'multipart/form-data'
                    ]

// URL Mapping Cache Max Size, defaults to 5000
//grails.urlmapping.cache.maxsize = 1000

// The default codec used to encode data with ${}
grails.views.default.codec = "html" // none, html, base64
grails.views.gsp.encoding = "UTF-8"
grails.converters.encoding = "UTF-8"
// enable Sitemesh preprocessing of GSP pages
grails.views.gsp.sitemesh.preprocess = true
// scaffolding templates configuration
grails.scaffolding.templates.domainSuffix = 'Instance'

// Set to false to use the new Grails 1.2 JSONBuilder in the render method
grails.json.legacy.builder = false
// enabled native2ascii conversion of i18n properties files
grails.enable.native2ascii = true
// whether to install the java.util.logging bridge for sl4j. Disable for AppEngine!
grails.logging.jul.usebridge = true
// packages to include in Spring bean scanning
grails.spring.bean.packages = []

// request parameters to mask when logging exceptions
grails.exceptionresolver.params.exclude = ['password']

// set per-environment serverURL stem for creating absolute links
environments {
    production {
        grails.serverURL = "http://www.changeme.com"
    }
    development {
        grails.serverURL = "http://localhost:8080/${appName}"
    }
    test {
        grails.serverURL = "http://localhost:8080/${appName}"
    }

}

// log4j configuration
import org.springframework.core.io.ClassPathResource
import org.springframework.core.io.support.PropertiesLoaderUtils
def properties

try {
	properties = PropertiesLoaderUtils.loadProperties(new ClassPathResource("app-config.properties"))
} catch (IOException e) {
	e.printStackTrace();
}
log4j = {
	appenders {
		appender new org.apache.log4j.DailyRollingFileAppender(name:"dailyAppender",layout:pattern(conversionPattern: '%c{2} %m%n'),fileName:"/home/<USER>/logs/cnvd_qihoo.log",datePattern:"'.'yyyy-MM-dd")
	}

	root{
		info 'stdout'
		error 'dailyAppender'
		info 'dailyAppender'
		//debug 'dailyAppender'
		additivity=true
	}


}

grails {
	mail {
		host = properties.getProperty("mail.smtp")
		port = properties.getProperty("mail.smtpPort")
		username = properties.getProperty("mail.username")
		password = properties.getProperty("mail.password")
		flawSendEmail = properties.getProperty("mail.flawSendEmail")
		props = [ "mail.smtp.auth":"true",
			"mail.smtp.socketFactory.port":"465",
			"mail.smtp.socketFactory.class":"javax.net.ssl.SSLSocketFactory",
			"mail.smtp.socketFactory.fallback":"false"  ]
	}
}

grails.mail.default.from=properties.getProperty("mail.from")

filePath{
	BSCertificateFilePath = properties.getProperty("filePath.BSCertificateFilePath")
	YCCertificateFilePath = properties.getProperty("filePath.YCCertificateFilePath")
	BSCertemplateFilePath = properties.getProperty("filePath.BSCertemplateFilePath")
	YCCertemplateFilePath = properties.getProperty("filePath.YCCertemplateFilePath")
	signaturePath = properties.getProperty("filePath.signaturePath")
	flawAttFilePath = properties.getProperty("filePath.flawAttFilePath")
	patchInfoAttFilePath = properties.getProperty("filePath.patchInfoAttFilePath")
	exploitAttFilePath = properties.getProperty("filePath.exploitAttFilePath")
	webinfoAttFilePath = properties.getProperty("filePath.webinfoAttFilePath")
	taskAttFilePath = properties.getProperty("filePath.taskAttFilePath")
	flawApplyAttFilePath = properties.getProperty("filePath.flawApplyAttFilePath")
	webinfoContentFilePath = properties.getProperty("filePath.webinfoContentFilePath")
	batchFlawFilePath = properties.getProperty("filePath.batchFlawFilePath")
	cerFilePath = properties.getProperty("filePath.cerFilePath")
	TxtFlawPath = properties.getProperty("filePath.TxtFlawPath")
	TxtProductPath = properties.getProperty("filePath.TxtProductPath")
	TxtReferencePath = properties.getProperty("filePath.TxtReferencePath")
	TxtDomainNamePath = properties.getProperty("filePath.TxtDomainNamePath")
	TxtIpPath = properties.getProperty("filePath.TxtIpPath")
	TxtIndustryPath = properties.getProperty("filePath.TxtIndustryPath")
	importJsonFilePath = properties.getProperty("filePath.importJsonFilePath")
}

url {
	qihooApiUrl=properties.getProperty("api.qihoo.send.url")
	starttime=properties.getProperty("api.qihoo.starttime")
	//endtime=properties.getProperty("api.qihoo.endtime")
	startFlag=properties.getProperty("api.qihoo.completion.data.job.flag")
	queryInterface=properties.getProperty("flaw.url.query.interface")
	saveInterface=properties.getProperty("flaw.url.save.interface")
	qihooUserId=properties.getProperty("api.qihoo.user.id")
}
grails.date.formats = ["yyyy-MM-dd HH:mm:ss","yyyy-MM-dd'T'HH:mm:ss","yyyy-MM-dd","yyyy-MM-dd HH:mm:ss.SSS ZZZZ", "dd.MM.yyyy HH:mm:ss"];
