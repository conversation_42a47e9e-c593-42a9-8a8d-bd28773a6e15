import java.io.IOException

import org.springframework.core.io.ClassPathResource
import org.springframework.core.io.support.PropertiesLoaderUtils

def pros

try {
	pros = PropertiesLoaderUtils.loadProperties(new ClassPathResource("app-config.properties"))
} catch (IOException e) {
	e.printStackTrace();
}

dataSource {
	pooled = true
	driverClassName = pros.getProperty("jdbc.driverClassName")
	username = pros.getProperty("jdbc.username")
	password = pros.getProperty("jdbc.password")
	loggingSql=true
	dialect = org.hibernate.dialect.MySQLDialect
	properties {
		validationQuery="SELECT 1"
		maxActive = 5000  //500
		maxIdle = 30  //25
		minIdle = 5
		initialSize = 10  //5
		minEvictableIdleTimeMillis = 60000 //60000
		timeBetweenEvictionRunsMillis = 60000 //60000
		maxWait = -1 //10000
		testWhileIdle = true
		removeAbandoned = true
	}
}
hibernate {
	cache.use_second_level_cache=true
	cache.use_query_cache=true
	cache.provider_class='net.sf.ehcache.hibernate.EhCacheProvider'
	show_sql=false
}
// environment specific settings
environments {
	development {
		dataSource {
			dbCreate = "update" // one of 'create', 'create-drop','update'
			url = pros.getProperty("jdbc.url")
		}
	}
	test {
		dataSource {
			dbCreate = "update"
			url = pros.getProperty("jdbc.url")
		}
	}
	production {
		dataSource {
			dbCreate = "update"
			url = pros.getProperty("jdbc.url")
		}
	}
}