package com.cnvd.acl

import java.util.Date;
import java.util.Set;

class User {
	String userName //用户名
	String password//密码
	Integer enable=1 //是否启用 1启用 0禁用
	Integer sortPriority
	Date dateCreated //创建日期
	Date lastUpdated //更新日期
	Map<String,Resource> permissionResource
	Map<String,Integer> permissionAction

	static transients = ['permissionResource', 'permissionAction']
	static constraints = {
		userName blank: false, unique: true
		password blank:false

	}

	Set<Role> getRoles() {
		UserRole.findAllByUser(this).collect { it.role } as Set
	}
}
