package com.cnvd.acl

import java.io.Serializable;

class RoleResource implements Serializable{
	Role role
	Resource resource
    static constraints = {
    }
	static RoleResource get(long resourceId, long roleId) {
		find 'from RoleResource where resource.id=:resourceId and role.id=:roleId',
			[resourceId: resourceId, roleId: roleId]
	}

	static RoleResource create(Role role, Resource resource, boolean flush = false) {
		def ss=new RoleResource(resource: resource, role: role).save(flush: flush, insert: true)
		if(ss!=null){
			println ss.errors
		}
	}

	static boolean remove(User resource, Role role, boolean flush = false) {
		RoleResource instance = RoleResource.findByResourceAndRole(resource, role)
		if (!instance) {
			return false
		}

		instance.delete(flush: flush)
		true
	}

	static void removeAll(User resource) {
		executeUpdate 'DELETE FROM RoleResource WHERE resource=:resource', [resource: resource]
	}

	static void removeAll(Role role) {
		executeUpdate 'DELETE FROM RoleResource WHERE role=:role', [role: role]
	}

	static mapping = {
		id composite: ['role', 'resource']
		version false
	}
}
