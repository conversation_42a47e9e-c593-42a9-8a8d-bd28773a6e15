package com.cnvd.acl

import java.util.Set;

class Resource {
	String resourceName
	Integer orderNum=0
	String url
	String permissionName
	Integer isMenu=0
	Integer parent=0
	String actionNames
	
	
    static constraints = {
		resourceName blank:false
		url nullable:true
		permissionName nullable:true
		parent nullable:true
		actionNames nullable:true
		
    }
	def getChildResources() {
		Resource.findAllByParent(this.id,[sort:"orderNum"])
	}
}
