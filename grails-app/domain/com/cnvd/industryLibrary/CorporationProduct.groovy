package com.cnvd.industryLibrary

import com.cnvd.acl.User
import com.cnvd.productInfo.Manufacturer
import com.cnvd.productInfo.ProductCategory
import com.cnvd.productInfo.ProductEdition
import com.cnvd.productInfo.ProductInfo

/**
 * 行业厂商资产domain类
 * <AUTHOR>
 *
 */
class CorporationProduct {
	
	Corporation corporation //行业厂商
	Manufacturer manufacturer //产品厂商
	ProductCategory productCategory //产品类别
	String edition //版本 
	Date dateCreated
	Date lastUpdated
	User creater //创建者
    static constraints = {
		productCategory(nullable:true)
		edition(nullable:true)
    }
}
