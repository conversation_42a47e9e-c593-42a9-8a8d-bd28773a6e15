package com.cnvd.productInfo

import java.util.Date;

import com.cnvd.flawInfo.FlawProduct

class ProductInfo {
	Manufacturer manufacturer
	ProductCategory productCategory
	String edition
	Date dateCreated //创建日期
	Date lastUpdated //更新日期
	String name
	String description
	static hasMany = [flawProducts:FlawProduct]
	

    static constraints = {
		name(blank:false,unique:true)
		description(nullable:true)
		manufacturer(blank:false)
		productCategory(blank:false)
		
    }
	def beforeDelete = {
		println "ttttttttttttttttttttttttttttttttttt"
		//删除所有厂商下的产品类别
		executeUpdate 'DELETE FROM FlawProduct WHERE product=:product', [product: this]
		//executeUpdate 'DELETE FROM Asset WHERE product = :product',[product:this]
		
	}
}
