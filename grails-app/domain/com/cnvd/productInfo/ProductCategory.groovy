package com.cnvd.productInfo

import java.util.Date;

class ProductCategory {
	Manufacturer manufacturer //厂商
	String name//产品名称
	String description//产品描述
	Date dateCreated //创建日期
	Date lastUpdated //更新日期
    static constraints = {
		name(blank:false)
		description(nullable:true)
    }
	def beforeDelete = {

		executeUpdate 'DELETE FROM FlawProduct WHERE product.id in (select id from ProductInfo where productCategory=:productCategory)', [productCategory: this]
		//executeUpdate 'DELETE FROM ProductEdition WHERE productCategory=:productCategory', [productCategory: this]
	
		executeUpdate 'DELETE FROM ProductInfo WHERE productCategory=:productCategory', [productCategory: this]
		
	}
}
