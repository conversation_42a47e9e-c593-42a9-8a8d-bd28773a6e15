package com.cnvd.productInfo

import java.util.Date;

class ProductEdition {

	ProductCategory productCategory
	Manufacturer manufacturer 
	
	String name
	String description
	Date dateCreated //创建日期
	Date lastUpdated //更新日期
    static constraints = {
		name(blank:false,unique:true)
		description(nullable:true)
    }
	def beforeInsert = {
		manufacturer=productCategory.manufacturer
	}
	def beforeDelete={
		executeUpdate 'DELETE FROM FlawProduct WHERE product.id in (select id from ProductInfo where productEdition=:productEdition)', [productEdition: this]
		executeUpdate 'DELETE FROM ProductInfo WHERE productEdition=:productEdition', [productEdition: this]
	}
}
