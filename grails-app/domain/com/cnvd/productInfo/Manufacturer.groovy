package com.cnvd.productInfo

class Manufacturer {

	String name  //厂商名称

	String description //厂商详细信息
	String fromWhere //厂商来源
	Date dateCreated //创建日期
	Date lastUpdated //更新日期
	
    static constraints = {
		name(blank:false,unique:true)
		description(nullable:true)
        fromWhere(nullable:true)

	 }
	def beforeDelete = {
		//删除所有厂商下的产品类别\
		executeUpdate 'DELETE FROM FlawProduct WHERE product.id in (select id from ProductInfo where manufacturer=:manufacturer)', [manufacturer: this]
		
		executeUpdate 'DELETE FROM ProductInfo WHERE manufacturer=:manufacturer', [manufacturer: this]
		//executeUpdate 'DELETE FROM ProductEdition WHERE manufacturer=:manufacturer', [manufacturer: this]
		executeUpdate 'DELETE FROM ProductCategory WHERE manufacturer=:manufacturer', [manufacturer: this]
	
		
	}
}
