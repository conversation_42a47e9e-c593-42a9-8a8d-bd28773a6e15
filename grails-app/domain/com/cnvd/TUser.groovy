package com.cnvd

import com.cnvd.acl.User
import com.cnvd.productInfo.Manufacturer
import com.cnvd.util.Constants

class TUser {
	String email    //邮箱
	String nickName //用户昵称
	String address  //地址
	String work		//工作
	String password //密码
	String description //个人描述
	Integer gender=1200 //性别
	Date dateCreated //创建时间
	Date registTime //注册时间
	Date lastUpdated //更新时间
	Integer userType //用户类别(100200:个人100201:成员单位100202:合作伙伴100203:企业用户 )
	String phoneNumber //移动电话
	Integer status	//用户状态(100300：待审核 10301：未激活 100302：停用 100304：正常 100305:临时用户)
	String activeCode //激活码
	Integer integValue=0 //积分值
	String url
    User creatUser
    User updateUser
    Manufacturer manufacturer ////企业用户的厂商绑定
	String seriesNum //报送单位编号
	String userName //用户的真实姓名，如果是成员单位则表示单位的全称
	String workplace //用户的工作单位(只适用于个人用户)
	Integer enable=1
    TUser unitMem //个人用户所属的成员单位
//	Integer tag=0 //0表示没有进行推送1代表进行推送了
	
	//字段约束
	static constraints = {
		email(nullable: true, unique: true, email:true)
		nickName(nullable:true)
		lastUpdated(nullable: true)
		address(nullable: true)
		work(nullable: true)
		password(blank: false)
		description(nullable:true)
		userType(nullable:true)
		gender(nullable: true)
		status(nullable:true)
		activeCode(nullable:true)
		phoneNumber(nullable: true)
		integValue(nullable:true)
		url(nullable:true)
		creatUser(nullable:true)
		updateUser(nullable:true)
		manufacturer(nullable:true)
		seriesNum(nullable:true)
		userName(nullable:true)
		workplace(nullable:true)
		enable(nullable:true)
		unitMem(nullable:true)
		registTime(nullable:true)
	}
	def beforeInsert() {
		encodePassword()
		if(userType==100201){
			def hql = "select max(seriesNum) from TUser"
			def maxNumberStr = TUser.executeQuery(hql)[0]
			if(maxNumberStr==null){
				seriesNum="01"
			}else{
				seriesNum=String.format("%02d", Integer.parseInt(maxNumberStr)+1);
			}
		}
	}

	def beforeUpdate() {
		if (isDirty('password')) {
			encodePassword()
		}
		if(userType==100201 && (seriesNum==null || seriesNum=="" )){
			def hql = "select max(seriesNum) from TUser"
			def maxNumberStr = TUser.executeQuery(hql)[0]
			if(maxNumberStr==null){
				seriesNum="01"
			}else{
				seriesNum=String.format("%02d", Integer.parseInt(maxNumberStr)+1);
			}
		}
	}
	
	protected void encodePassword() {
		password = MD5.getEncryptedPwd(password)
	}
	String  userTypeStr(){

		if(userType==Constants.userTypeMap['PERSONAL']){
			return "个人用户"
		}else if(userType==Constants.userTypeMap['MEMUNIT']){
			return "成员单位"
		}else if(userType==Constants.userTypeMap['MANUFACTURER']){
			return "生产厂商"
		}else if(userType==Constants.userTypeMap['ENTERPRISEUSER']){
			return "企业用户"
		}else {
			return "合作伙伴"
		}
	}
	String  statusStr(){
		if(status==Constants.userStatusMap['PENDING']){
			return "待审核"
		}else if(status==Constants.userStatusMap['UNACTIVE']){
			return "未激活"
		}else if (status==Constants.userStatusMap['ACTIVE']){
			return "正常"
		}else if(status==Constants.userStatusMap['DISABLED']){
			return "停用"
		}else if(status==Constants.userStatusMap['TEMPORARY']){
			return "临时用户"
		}
	}
}
