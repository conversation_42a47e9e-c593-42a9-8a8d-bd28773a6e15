package com.cnvd.info

import com.cnvd.TUser
import com.cnvd.acl.User

/**
 * 消息系统实体类
 * <AUTHOR>
 *
 */
class News {
	User user	//后台管理员
	Date dateCreated
	Date lastUpdated
	String title //消息标题
	String content //消息内容
	Integer type //消息类型 1证书消息 0系统消息 2漏洞审核通过
	static mapping = {
		content type:'text'
	}
	
    static constraints = {
		user(nullable:true)
		content(nullable:true)
    }
	def typeStr(){
		if(type==1){
			return "证书消息"
		}
		if(type==0){
			return "系统消息"
		}
	}
}
