package com.cnvd.info

import com.cnvd.MD5
import com.cnvd.common.Attachment
import com.cnvd.util.Constants


class Webinfo {

    String title//文章标题
	
	String content//文章内容
	
	Integer sort=0
	
	Integer status=0 //发布状态 0表示未发布 1表示发布
	
	String fileUrl
	
	String fileName
	
	Integer type//栏目名称
	
	Date dateCreated//创建时间
	
	Date lastUpdated//更新时间
	
	String titlePicUrl
	
	String titleInfo//文章副标题
	
	String creator//处理者
	
	String keyword//关键字
	
	String source //文章来源
	
	Attachment attachment
	
	String securityReportId //安全公告编号
	
	Date releaseTime //发布时间
	
	Integer isRecommend = 0 //热点推荐标志位  0表示否 1表示是
	String downCode
	
	Integer userGroupTypeId = -1 // -1表示无组别 
	
	static searchable = true
	
	static mapping = {
		content type:'text'
	}
	
    static constraints = {
		fileUrl(nullable:true)
		type(nullable:true)
		fileName(nullable:true)
		status(nullable:true)
		sort(nullable:true)
		content(nullable:false)
		titlePicUrl(nullable:true)
		title(nullable:false)
		titleInfo(nullable:true)
		attachment(nullable:true)
		lastUpdated(nullable:true)
		creator(nullable:true)
		keyword(nullable:true)
		source(nullable:true)
		securityReportId(nullable:true)
		releaseTime(nullable:true)
		isRecommend(nullable:true)
		downCode(nullable:true)
    }
	static void removeAll(Webinfo id) {
		executeUpdate 'DELETE FROM Webinfo WHERE webinfo=:id', [webinfo: id]
		print("=============id"+id)
	}
	
	def afterInsert={
		println "afterInsert"
		downCode = MD5.getMD5Str(MD5.getMD5Str(String.valueOf(id))+MD5.getMD5Str(Constants.getRandomNumber(6).toString()));
		//0表示未发布 1表示发布
		if(status == 1 && releaseTime==null){
			releaseTime = new Date()
			this.save()
		}
		if(type==14 && securityReportId == null){
			StringBuffer sb=new StringBuffer();
			Calendar cal=Calendar.getInstance();//使用日历类
			int year=cal.get(Calendar.YEAR);//得到年
			def hql = "select max(securityReportId) from Webinfo where securityReportId like 'CNTA-"+year+"%'"
			def maxNumberStr = Webinfo.executeQuery(hql)[0]
			if(maxNumberStr==null){
				securityReportId="CNTA-"+year+"-"+ String.format("%04d", 1);
			}else{
				def numberInt = Integer.parseInt(maxNumberStr.substring(maxNumberStr.length()-4,maxNumberStr.length()))+1
				securityReportId="CNTA-"+year+"-"+ String.format("%04d", numberInt);
			}
			this.save();
		}
	}
	def beforeUpdate={
		
		def dirtyProperties = this.dirtyPropertyNames
		if (dirtyProperties.contains('status') && status == 1) {
			releaseTime = new Date()
			this.save()
		} 
		//0表示未发布 1表示发布
		if(status == 1 && releaseTime==null){
			releaseTime = new Date()
			this.save()
		}
		if(type==14 && securityReportId == null){
			StringBuffer sb=new StringBuffer();
			Calendar cal=Calendar.getInstance();//使用日历类
			int year=cal.get(Calendar.YEAR);//得到年
			def hql = "select max(securityReportId) from Webinfo where securityReportId like 'CNTA-"+year+"%'"
			def maxNumberStr = Webinfo.executeQuery(hql)[0]
			if(maxNumberStr==null){
				securityReportId="CNTA-"+year+"-"+ String.format("%04d", 1);
			}else{
				def numberInt = Integer.parseInt(maxNumberStr.substring(maxNumberStr.length()-4,maxNumberStr.length()))+1
				securityReportId="CNTA-"+year+"-"+ String.format("%04d", numberInt);
			}
			this.save();
		}
	}
}
