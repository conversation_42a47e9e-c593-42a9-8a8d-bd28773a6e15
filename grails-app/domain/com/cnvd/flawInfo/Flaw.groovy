package com.cnvd.flawInfo

import com.cnvd.MD5
import com.cnvd.Points
import com.cnvd.TUser
import com.cnvd.acl.User
import com.cnvd.common.Attachment
import com.cnvd.patchInfo.PatchInfo
import com.cnvd.productInfo.Manufacturer
import com.cnvd.util.Constants
import com.cnvd.util.SQLUtil

class Flaw {
	String title // 标题
	String number // 漏洞编号
	Integer isFirst //是否是首次公开 1是 0不是
	Integer isZero //是否零日漏洞 1是 0不是
	Date foundTime //发现时间
	Date dateCreated //创建日期
	Date lastUpdated //更新日期
	Integer rank=0
	Integer status=1  //1一级审核2二级审核3三级审核9已经归档 -2作废 -1驳回 4补录漏洞未提交 5补录待审核漏洞
	Integer enable=1  //1可用 0删除
	Integer causeId  // 漏洞产生原因
	Integer threadId  //漏洞引发的威胁
	Integer serverityId //漏洞严重程度
	Integer positionId //漏洞利用的攻击位置
	Integer softStyleId //漏洞影响对象类型
	Integer viewCount=0  //漏洞查看次数
    TUser user  //前台上传用户
    User creator  //后台上传管理员
	Integer isu  //是否前台上报0 是  1否
	Integer isg=0//是否可以直接归档
	Integer isv=0 //是否验证  1验证 0 不验证 2验证中
	Integer isHot=0 //是否是热点漏洞 1是 0不是
	Integer isOriginal //是否原创 0是 1不是 2未知
	Integer ivp=0 //是否处置  1处置 0 不处置 2处置中
	Integer isp=0 //是否补丁  1已收录补丁 0 添加补丁 2收录补丁中
	Integer czff=0 //是否处置分发  0待处置分发 1已处置分发
    Flaw parentFlaw //关联漏洞
	Integer lastAction = 1 //1通过 2 驳回
	Date submitTime //漏洞报送时间
	Date storageTime  //归档时间
	Date openTime  //对外开放时间
    BaseMetric basemetric
    TemporalMetric temporalMetric
    EnvironmentalMetric environmentalMetric
    Manufacturer manufacturer  //所属厂商;
    Attachment attachment //漏洞附件
	Integer isAttShow //漏洞附件是否显示 0否 1是
    DetailedInfo detailedInfo
	String hangye //行业
	String discovererName //发现者姓名
	String referenceLink //漏洞参考链接
	Date firstTime
	Date secondTime
	Date thirdTime
	Integer isOpen //是否公开
	Integer isEvent=0 //是否为事件型漏洞 0 否 1 是
	Integer clickNum=0 //漏洞点击数
	Integer commentCount=0 //评论数量
	Integer concernCount=0 //漏洞关注数量
	String downCode
    BatchFlaw batchFlaw //批量上报漏洞id号，用于标识此漏洞是否为批量上传
	String oldNumber //记录原CNVD编号
	String tempNumber //记录临时编号
	Integer isAdditional=0  //是否为补录漏洞0 否 1 是
    Points points //原创漏洞积分奖金 zp
	Integer parentId //排序标识
	Integer flawTypesId //漏洞类型
	static constraints = {
		number(nullable:true)
		rank(nullable:true)
		isFirst (nullable:true)
		isZero(nullable:true)
		causeId  (nullable:true)
		threadId (nullable:true)
		serverityId (nullable:true)
		positionId (nullable:true)
		softStyleId (nullable:true)
		viewCount(nullable:true)
		user(nullable:true)
		creator(nullable:true)
		isHot(nullable:true)
		isu(nullable:true)
		isg(nullable:true)
		isv(nullable:true)
		ivp(nullable:true)
		isp(nullable:true)
		parentFlaw(nullable:true)
		lastAction(nullable:true)
		submitTime(nullable:true)
		basemetric(nullable:true)
		temporalMetric(nullable:true)
		environmentalMetric(nullable:true)
		storageTime(nullable:true)
		openTime(nullable:true)
		manufacturer(nullable:true)
		attachment(nullable:true)
		isOriginal(nullable:true)
		hangye(nullable:true)
		discovererName(nullable:true)
		referenceLink(nullable:true)
		firstTime(nullable:true)
		secondTime(nullable:true)
		thirdTime(nullable:true)
		isOpen(nullable:true)
		isEvent(nullable:true)
		clickNum(nullable:true)
		commentCount(nullable:true)
		concernCount(nullable:true)
		downCode(nullable:true)
		batchFlaw(nullable:true)
		isAttShow(nullable:true)
		oldNumber(nullable:true)
		tempNumber(nullable:true)
		points(nullable:true)
		parentId(nullable:true)
		flawTypesId(nullable: true)
	}
	//插入之前，系统自动生成漏洞编号
	def beforeInsert={
		isu=1
		Calendar cal=Calendar.getInstance();//使用日历类
		int year = cal.get(Calendar.YEAR);//得到年
		def tempNumSql = "select _nextval('flaw_id_" + year + "') as num"
		def tempNumRes = SQLUtil.getResult(tempNumSql,[])
		def maxTempNumStr=1
		if(tempNumRes == null || tempNumRes[0] == null || tempNumRes[0].num==null){
			String sql = "INSERT INTO cnvd_flaw_sequence(NAME,current_value,_increment) VALUES('flaw_id_"+year+"',1,1)"
			SQLUtil.execute(sql)
		}else{
			maxTempNumStr = tempNumRes[0].num
		}
		def numberInt = maxTempNumStr
		tempNumber = "CNVD-C-" + year + "-" + String.format("%05d", numberInt);
		downCode = MD5.getMD5Str(MD5.getMD5Str(String.valueOf(id))+MD5.getMD5Str(Constants.getRandomNumber(6).toString()));
	//	this.save()
		 }



		/*def unlockSql = "unlock tables;"
		 SQLUtil.execute(unlockSql);*/
	/*}*/

	def beforeDelete = {
		FlawProduct.executeUpdate("delete from FlawProduct where flaw=?",[this])
        FlawUpdated.executeUpdate("delete from FlawUpdated where flaw = ?",[this])
        PatchInfo.executeUpdate("delete from PatchInfo where flaw = ?",[this])
        Exploit.executeUpdate("delete from Exploit where flaw = ?",[this])
        FlawProcess.executeUpdate("delete from FlawProcess where flaw = ?",[this])
        ReferenceInfo.executeUpdate("delete from ReferenceInfo where flaw = ?",[this])
        ExmaineHistory.executeUpdate("delete from ExmaineHistory where flaw = ?",[this])
	}

	/*def beforeInsert={ isu=1 }*/
	def serverityIdStr(){
		def dictionaryInfo= DictionaryInfo.get(serverityId)
		return dictionaryInfo?.name
	}
	def statusStr(){
		if(status==1){
			if(lastAction==1){
				return "一级审核"
			}else if(lastAction==2){
				return "驳回待审"
			}
		}else if(status==2){
			return "二级审核"
		}else if(status==-1){
			return "驳回"
		}else if(status==-2){
			return "作废"
		}else if(status==3){
			return "三级审核"
		}else if(status==9){
			return "已归档"
		}else if(status==4){
			return "补录漏洞"
		}else if(status==5){
			return "补录待审核"
		}
	}
	def getExploit(){
		return Exploit.findByFlawAndEnable(this,1)
	}
	def getPatchInfo(){
		return PatchInfo.findByFlawAndEnable(this,1)
	}
	def getProduct(){
		def products= FlawProduct.findAllByFlaw(this)
	}
}
