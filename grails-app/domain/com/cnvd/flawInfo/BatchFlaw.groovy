package com.cnvd.flawInfo

import com.cnvd.MD5
import com.cnvd.TUser
import com.cnvd.common.Attachment
import com.cnvd.util.Constants

/**
 * 批量上报漏洞
 * <AUTHOR>
 */
class BatchFlaw {
	TUser tuser //上报人
	Date dateCreated 
	Date lastUpdated
	Attachment attachment //上传的附件
	String downCode
	Integer status=0 //0表示该zip包没有入库，1表示zip包已入库
    static constraints = {
		downCode(nullable:true)
    }
	
	def afterInsert = {
		downCode = MD5.getMD5Str(MD5.getMD5Str(String.valueOf(id))+MD5.getMD5Str(Constants.getRandomNumber(6).toString()))
		this.save()
	}
}
