package com.cnvd.flawInfo

import com.cnvd.TUser
import com.cnvd.acl.User

class ExploitLog {
	Date dateCreated
	Integer flawId
	Integer type  //1后台验证2前台验证3后台代替前台验证
	Integer status //1下发验证任务，2提交验证任务，3审核任务;4添加验证
	Integer result //结果 1通过审核 2 未通过审核
	User suser //审核人
	User xuser//下发任务人
	TUser tuser //前台验证用户
	User huser //后台验证用户
	
    static constraints = {
		suser(nullable:true)
		xuser(nullable:true)
		tuser(nullable:true)
		huser(nullable:true)
		
    }
}
