package com.cnvd.flawInfo

import com.cnvd.MD5
import com.cnvd.TUser
import com.cnvd.acl.User
import com.cnvd.common.Attachment
import com.cnvd.util.Constants

class Certificate {

    String c_id //证书编号
	Date awardTime //发证时间
	String awardCompany //发证单位
	Flaw flaw //对应的漏洞
	LeakType leakType //漏洞类型
	TUser tuser //前台漏洞上报者
	User user  //生成证书者
	Date dateCreated //生成时间
	Attachment pdfAttachment //证书附件(pdf)
	Attachment signatureAttachment //证书签名附件
	byte cerType //用于标识证书的类型 1为报送证书 2为原创证书
	Integer isOld=0
	String downCode
	
    static constraints = {
		flaw(nullable:true)
		user(nullable:true)
		leakType(nullable:true)
		pdfAttachment(nullable:true)
		signatureAttachment(nullable:true)
		isOld(nullable:true)
		downCode(nullable:true)
    }
	def afterInsert = {
		downCode = MD5.getMD5Str(MD5.getMD5Str(String.valueOf(id))+MD5.getMD5Str(Constants.getRandomNumber(6).toString()))
		this.save()
	}
}
