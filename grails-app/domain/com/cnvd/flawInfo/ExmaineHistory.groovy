package com.cnvd.flawInfo

import com.cnvd.acl.User

class ExmaineHistory {

	User user
	Date dateCreated
	Integer status
	Integer action
	String content
	Flaw flaw
	
    static constraints = {
    }
	
	def statusStr(){
		if(status==1){
			return "一级审核"
		}else if(status==2){
			return "二级审核"
		}else if(status==-2){
			return "作废"
		}else if(status==3){
			return "三级审核"
		}else if(status==9){
			return "已归档"
		}else if(status==5){
			return "补录审核"
		}
	}
	def actionStr(){
		if(action==1){
			return "审核通过"
		}else if(action==-1){
			return "驳回"
		}else if(action==-2){
			return "作废"
		}else if(action==2){
			return "验证"
		}
	}
}
