package com.cnvd.flawInfo

import com.cnvd.MD5
import com.cnvd.TUser
import com.cnvd.acl.User
import com.cnvd.common.Attachment
import com.cnvd.util.Constants

class Exploit {
	Integer exploitType //验证类型 1后台2前台3后台代替前台
	String exploitName //验证名称
	User user //后台验证用户
	TUser tuser //前台验证用户
	String concept //验证原理
	String poc //验证poc
	String suggestion//验证建议
	Date exploitTime //验证日期
	Date dateCreated //创建日期
	Date lastUpdated //更新日期
	Flaw flaw
	Integer status //验证状态 1通过审核2等待审核3未通过审核4未提交
	String content //前台验证 后台审核意见
	Attachment attachment //验证附件
	Integer enable=1 //1表示可用 0表示禁用
	String referenceLink //验证信息参考
	String downCode
    static constraints = {
		tuser(nullable:true)
		suggestion(nullable:true)
		poc(nullable:true)
		concept(nullable:true)
		exploitTime(nullable:true)
		exploitName(nullable:true)
		content(nullable:true)
		attachment(nullable:true)
		user(nullable:true)
		referenceLink(nullable:true)
		downCode(nullable:true)
    }
	
	static mapping = {
		attachment lazy:false
	}
	
	def afterInsert = {
		downCode = MD5.getMD5Str(MD5.getMD5Str(String.valueOf(id))+MD5.getMD5Str(Constants.getRandomNumber(6).toString()));
		this.save()
	}
	def getExploitTypeStr(){
		if(exploitType==1){
			return "后台验证"
		}else if(exploitType==2){
			return "前台验证"
		}else {
			return "后台代替前台验证"
		}
	}
	def getExploitUserName(){
		if(exploitType==1){
			return user?.userName
		}else{
			return tuser?.nickName
		
		}
	}
	def getExploitStatusStr(){
		if(status==1){
			
			return "通过审核"
		}else if(status==2){
			return "等待审核"
		
		}else if(status==3){
			return "未通过审核"
		}else if(status==4){
			return "未提交"
		}
		
	}
}
