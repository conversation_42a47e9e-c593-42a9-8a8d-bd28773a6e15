package com.cnvd

import com.cnvd.util.Constants

class IntegralInfo {
	TUser user
	Integer integralType
	Integer integralValue
	Date dateCreated
    static constraints = {
		
    }
	def getIntegralTypeStr(){
		if(integralType==Constants.exploitScoreType){
			return "上报验证"
		}else if(integralType==Constants.flawScoreType){
			return "上报漏洞"
		}else if(integralType==Constants.concernedType){
			return "漏洞被关注"
		}else if(integralType==Constants.commentByYouType){
			return "发表评论"
		}else if(integralType == Constants.commentToYouType){
			return "漏洞被评论"
		}
	}
}
