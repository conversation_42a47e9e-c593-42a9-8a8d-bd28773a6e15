package com.cnvd

/***
 * 
 *
 INSERT INTO resource(version,action_names,is_menu,order_num,parent,permission_name,resource_name,url) 
 VALUES (0,NULL,1,2,0,'userGroupType_manager','用户组别管理',null)
 INSERT INTO resource(version,action_names,is_menu,order_num,parent,permission_name,resource_name,url) 
 VALUES (0,'userGroupType_list',2,1,321,'UserGroupType_list','用户组别','/userGroupType/list')
 INSERT INTO resource(version,action_names,is_menu,order_num,parent,permission_name,resource_name,url) 
 VALUES (0,'userGroupType_list',3,0,322,'userGroupType_list','用户组别列表',null)
 INSERT INTO resource(version,action_names,is_menu,order_num,parent,permission_name,resource_name,url) 
 VALUES (0,'userGroupType_create,userGroupType_save',3,0,322,'userGroupType_create','添加用户组别',null)
 INSERT INTO resource(version,action_names,is_menu,order_num,parent,permission_name,resource_name,url) 
 VALUES (0,'userGroupType_edit,userGroupType_update',3,0,322,'userGroupType_update','修改用户组别',null)
 INSERT INTO resource(version,action_names,is_menu,order_num,parent,permission_name,resource_name,url) 
 VALUES (0,'userGroupType_delete',3,0,322,'userGroupType_delete','删除用户组别',null)
 * <AUTHOR>
 *
 */
class UserGroupType {
	String name
	Date dateCreated
	Date lastUpdated
	static constraints = {
		name(blank:false,nullable:false,unique:true)
	}
	
	static mapping = {
		sort "lastUpdated"
	}
}
