package com.cnvd.cer

import com.cnvd.acl.User

/**
 * 证书角色实体类
 * <AUTHOR>
 */
class CARole {
	String name //角色名称
	User creater //创建人
	String field
	Date dateCreated
	Date lastUpdated
	Date startTime  //获得漏洞开始时间
	Date endTime //获得漏洞结束时间
	String softStyleIdStr //应用类型类别
	String industryIdStr //可获取的行业限制
	static mapping = {
		field type : 'text'
	}
    static constraints = {
		startTime(nullable:true)
    }
}
