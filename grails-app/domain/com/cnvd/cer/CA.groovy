package com.cnvd.cer

import com.cnvd.TUser
import com.cnvd.acl.User
import com.cnvd.common.Attachment

class CA {
	
	TUser tuser   //证书所有人
	User creater  //证书生成人
	CARole caRole //证书角色
	Date dateCreated //证书生成时间
	Integer type 	//证书类型  0根证书 1 服务器证书  2客户证书
	Integer validPeriod	//证书有效期(天数，从dateCreated开始算)
	String password //密码
	String cnStr //名字或姓氏
	String ouStr //组织单位名称
	String oStr  //组织名称
	String lStr  //区域名称
	String stStr //州 省份名称
	String cStr  //国家代码
	Integer isg=0  //是否生成了密钥库和信任库 0 未生成 1已生成
	String keystorePwd  //密钥库密码
	String truststorePwd //信任库密码
	Integer enable = 0  //证书是否被删除 0 未删除  1已删除
	String accessInterface  //证书所能访问接口
	  
    static constraints = {
		tuser(nullable:true)
		password(nullable:true)
		cnStr(nullable:true)
		ouStr(nullable:true)
		oStr(nullable:true)
		lStr(nullable:true)
		stStr(nullable:true)
		cStr(nullable:true)
		caRole(nullable:true)
		isg(nullable:true)
		keystorePwd(nullable:true)
		truststorePwd(nullable:true)
    }
}
