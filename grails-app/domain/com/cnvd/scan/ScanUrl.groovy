package com.cnvd.scan

import java.text.SimpleDateFormat
import java.util.Date;

import com.cnvd.TUser;

class ScanUrl {

    
	String number // URL编号
	String url	//URL名称
	TUser user //URL新建人
	String bugName //漏洞名称
	Integer status=1  //1未扫描 2扫描中 3已扫描 4审核中
	Integer statusDo=1 //1扫描  2正在扫描  3审核中 4可下载
	Date submitTime //URL上报时间
	Date scanTime //URL扫描时间
	Integer enable=1  //1可用 0删除 --软删除
	String scanTaskId  //扫描漏洞任务ID
	
	static constraints = {
		number(nullable:true)
		url(nullable:false)
		status(nullable:true)
		submitTime(nullable:true)
		scanTime(nullable:true)
		enable(nullable:true)
		user(nullable:false)
		bugName(nullable:true)
		scanTaskId(nullable:true)
		statusDo(nullable:true)
	}
	
	//插入之前，系统自动生成URL编号
	def beforeInsert={
//		isu=0
//		Calendar cal=Calendar.getInstance();//使用日历类
//		int year = cal.get(Calendar.YEAR);//得到年
//		def tempNumSql = "select _nextval('flaw_id_" + year + "') as num"
//		def tempNumRes = SQLUtil.getResult(tempNumSql,[])
//		def maxTempNumStr = tempNumRes[0].num
//		def numberInt = maxTempNumStr
//		tempNumber = "CNVD-C-" + year + "-" + String.format("%05d", numberInt);
//		downCode = MD5.getMD5Str(MD5.getMD5Str(String.valueOf(id))+MD5.getMD5Str(Constants.getRandomNumber(6).toString()));
		Date date = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		String str = sdf.format(date);
		number = "CNVD-" + str;
		

	}

}
