package com.cnvd.scan
/**
 * 扫描返回的结果。具体的漏洞信息
 */
import com.cnvd.TUser;

class ScanBug {
	TUser user //URL新建人
	String scanTaskId  //扫描漏洞任务ID
	String level //漏洞危害等级 1高危漏洞  2中危漏洞  3低危漏洞
	String name	//漏洞名称
	String url	//漏洞url
	String html	//推荐的展示漏洞的HTML格式
	String nickname	//文件内容泄漏漏洞(漏洞的中文名称)
	String harm	//漏洞危害简介
	String bugDescribe	//漏洞危害具体说明
	String solution	//修复方案
	String bugFrom	//漏洞参考信息
	
	static constraints = {
		user(nullable:true)
		scanTaskId(nullable:true)
		level(nullable:true)
		name(nullable:true)
		url(nullable:true)
		html(nullable:true)
		nickname(nullable:true)
		harm(nullable:true)
		bugDescribe(nullable:true)
		solution(nullable:true)
		bugFrom(nullable:true)
		
    }
}
