package com.cnvd.push

import java.util.Date;

class FlawPushRule {

	String ruleName // 规则名称
	Integer isEvent = 0 //是否事件型
	String selectTarget // 检索目标
	String selectField // 检索字段
	String keyword //或关键词
	String keywordAnd //与关键词
	String keywordNo //非关键词
	Integer keywordLogic // 关键字检索逻辑
	Integer keywordAndTargetLogic // 关键词与检索目标的逻辑
	String pushField // 推送的字段
	String pushEmail // 推送的email
	String emailTitle // 推送的邮件标题
	Integer isPush = 0
	
	Date dateCreated
	Date lastUpdated

	static constraints = {
	}
}
