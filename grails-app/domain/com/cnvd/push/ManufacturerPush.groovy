package com.cnvd.push

import java.util.Date;

class ManufacturerPush {

	String name //厂商名称
	String email // 厂商推送Email
	Date dateCreated
	Date lastUpdated
	Integer isSend = 1 // 是否推送 1 推送
	Integer type = 0 // 推送类型 0，全部，1,高危，2，银联事件型 ，3 中国移动事件型

	static constraints = {
		name(nullable: false,maxLength:255)
		email(nullable: false, unique: true, email:true)
		dateCreated()
		lastUpdated()
		isSend(nullable: false)
		type(nullable: false)
	}
}
