package com.cnvd
/**
 *用户荣誉值记录表
 * */
class TUserHonorValueRecord {
    TUser tUser //用户id
	Double beforeHonorValue //之前荣誉值
    Double currentHonorValue //现在荣誉值
    Double updateValue //荣誉变化值
	Date dateCreated //创建时间
    String remarks //备注
	Integer updateType //1自动  2手动
	Integer years //当前年月


	//字段约束
	static constraints = {
        tUser(nullable:true)
		beforeHonorValue(nullable:true)
		currentHonorValue(nullable:true)
        updateValue(nullable:true)
		dateCreated(nullable:true)
        remarks(nullable:true)
		updateType(nullable:true)
		years(nullable:true)
	}

}
