package com.cnvd
/**
 * 原创漏洞积分奖金
 */
class Points {

	Float coefficient //加分系数
	Float added //额外积分
	Float total //总分
	Float honorPoints //荣誉值积分
	Float isSynchronize=0 //是否同步积分 0否，1是
	Float honorValue //荣誉值
	Date dateCreated //创建日期
	Integer flawId //对应漏洞ID

    static constraints = {
		coefficient(nullable:true)
		added(nullable:true)
		total(nullable:true)
		honorPoints(nullable:true)
		isSynchronize(nullable:true)
		honorValue(nullable:true)
		dateCreated(nullable:true)
		flawId(nullable:true)
    }
}
