package main

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cnvd.MD5
import com.cnvd.TUser
import com.cnvd.UnicodeUtils
import com.cnvd.api.QihooApi
import com.cnvd.bugcloud.receive.Receiver
import com.cnvd.bugcloud.util.Util
import com.cnvd.common.Attachment
import com.cnvd.flawInfo.DetailedInfo
import com.cnvd.flawInfo.Flaw
import com.cnvd.flawInfo.FlawUrl
import com.cnvd.listener.QihooAll
import com.cnvd.listener.QihooOne
import com.cnvd.listener.SyncAll
import com.cnvd.listener.SyncOne
import com.cnvd.productInfo.Manufacturer
import com.cnvd.qihoo.HttpClientutils
import com.cnvd.wooyun.util.WordUtil
import org.apache.commons.io.FileUtils
import org.codehaus.groovy.grails.commons.ConfigurationHolder
import org.springframework.util.FileCopyUtils

import java.text.SimpleDateFormat
import java.util.regex.Matcher
import java.util.regex.Pattern

/**
 * 这个是奇安信数据导入的
 * 使用样例
 * http://localhost:8080/cnvd_crawler/qianxinImportFile/importFile?start_time='2022-08-12'&end_time='2022-08-12'
 */
class QianxinImportFileController {

    def importFile = {
        execute(params.start_time,params.end_time);
    }
    //修改为全量接口 ，因此6小时跑一次
    def timeout = 21600000l
    //每7天跑一次
    //def timeout = 604800000l
    def attachmentService
    def flawService
    def grailsApplication
    def static SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
    def static SimpleDateFormat sdf = new SimpleDateFormat("yyyy")

    def static config = ConfigurationHolder.config
    def static SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
    /*private static String getTimeStamp = "https://butian.360.cn/newApi/getTimeStamp";//获取目标服务器时间戳
    private static String getNewApi = "https://butian.360.cn/newApi";//补天获取数据接口*/
    private static String getTimeStamp = "https://api.butian.net/Interface/getTimeStamp";//获取目标服务器时间戳
    private static String getNewApi = "https://api.butian.net/Interface";//补天获取数据接口
    private final static String key = "EE5065448620E4386EE8F5DDC3F97CB8"; //key值
    def execute(String start_time,String end_time) {
        println "qihoo  job  start time = " + formatter.format(new Date())
        String t1 = "qihoo" + System.currentTimeMillis();  //生成批次号
        //获取目标服务器时间戳
        String requestMethod = "POST";
        String outputStr = null;
        String timestamp;
        try {
            //timestamp = RequestUtil.httpsRequest(getTimeStamp, requestMethod,outputStr);
            timestamp = HttpClientutils.doPostSSLToJson(getTimeStamp, new net.sf.json.JSONObject());
            println "时间戳=" + timestamp
        } catch (Exception e) {
            e.printStackTrace()
            //时间戳获取接口出错
            QihooAll qihooalls = new QihooAll()
            qihooalls.dateCreated = new Date()
            qihooalls.num = t1
            qihooalls.status = "error,获取奇虎时间戳接口访问出错!"
            qihooalls.save(flush: true)
            return;
        }
        //判断是否有返回值
        if (timestamp == null || "".equals(timestamp)) {
            println "时间戳获取错误"
            //获取漏洞信息接口出错
            QihooAll qihooalls = new QihooAll()
            qihooalls.dateCreated = new Date()
            qihooalls.num = t1
            qihooalls.status = "error,获取奇虎时间戳接口访问出错!"
            qihooalls.save(flush: true)
            return;
        }
        //调用数据获取接口
        String idStr = 1;
        //起始时间（UNIX时间戳格式），北京时间  eg:2017-01-01
//		String startTimeStr = sf.format(new Date())
//		Date ss = sf.parse(startTimeStr)
//		long ff = sf.parse(startTimeStr).getTime()
//		String start_time = String.valueOf(sf.parse(startTimeStr).getTime() / 1000L);
        //应补天接口方要求，日期为3天之内的 20191107 我方默认设置成4天内的数据了
        Calendar cStar = Calendar.getInstance();
        cStar.add(Calendar.DAY_OF_MONTH, -1);
        String startTimeStr = sf.format(cStar.getTime())
        def starttime="${grailsApplication.config.url.starttime}"
        if(starttime){
            startTimeStr=starttime//默认取当前时间，如果从新获取之前的数据，更改配置文件的starttime开始时间
        }
//        String start_time = String.valueOf(sf.parse(startTimeStr).getTime() / 1000L);

        //截止时间（UNIX时间戳格式），北京时间  eg:2017-01-02
        //当前日期加一天
        Calendar c = Calendar.getInstance();
        String endTimeStr = sf.format(c.getTime())
//        String end_time = String.valueOf(sf.parse(endTimeStr).getTime() / 1000L);


        //key,start_time,end_time,vulid,timestamp,id按顺序拼接。(key,timestamp,id为必填项)
        String tokenStr = key + start_time + end_time + timestamp + idStr;
        String token = MD5.getMD5Str(tokenStr).toUpperCase();
        //拼接post请求param
        String param = "id=" + idStr + "&timestamp=" + timestamp + "&token=" + token + "&start_time=" + start_time + "&end_time=" + end_time
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("id", idStr);
        params.put("timestamp", timestamp);
        params.put("token", token);
        params.put("start_time", start_time);
        params.put("end_time", end_time);
        //访问补天获取漏洞详情的接口
        String apiMethod = "POST";
        String content;
        try {
            System.out.println("startTime================"+startTimeStr+"|endTime=================="+endTimeStr);
            println "接口地址："+getNewApi + "?" + param
            // content = RequestUtil.httpsRequest(getNewApi, apiMethod, param);
            // content = HttpClientutils.doGet(getNewApi + "?" + param, new HashMap<String, Object>());
            content = HttpClientutils.newDoPost(getNewApi, param, "utf-8");
            println content
        } catch (Exception e) {
            //获取漏洞信息接口出错
            QihooAll qihooalls = new QihooAll()
            qihooalls.dateCreated = new Date()
            qihooalls.num = t1
            qihooalls.status = "error,获取奇虎漏洞接口数据出错!"
            qihooalls.save(flush: true)
            return;
        }
        //判断是否有返回值
        if (content == null || "".equals(content)) {
            //获取漏洞信息接口出错
            QihooAll qihooalls = new QihooAll()
            qihooalls.dateCreated = new Date()
            qihooalls.num = t1
            qihooalls.status = "error,获取奇虎漏洞接口访问出错!"
            qihooalls.save(flush: true)
            return;
        }
        //判断接口状态
        net.sf.json.JSONObject json = net.sf.json.JSONObject.fromObject(content);
        String code = json.getString("code");
        //日志
        new com.cnvd.qihoo.Receiver().log(content);
        if (code.equals("0000")) {
            //格式化漏洞内容
            String contArr = json.getString("data");
            //\u8bf7\u6c42\u6210\u529f,\u6570\u636e\u4e3a\u7a7a  请求成功,数据为空
            if (contArr.equals("")) {
                //获取漏洞信息接口出错
                QihooAll qihooalls = new QihooAll()
                qihooalls.dateCreated = new Date()
                qihooalls.num = t1
                qihooalls.status = "worning,接口访问成功，但数据为空，请联系接口提供人!"
                qihooalls.save(flush: true)
                return;
            }

            net.sf.json.JSONArray jsonArray;
            try {
                jsonArray = net.sf.json.JSONArray.fromObject(contArr);
//				//漏洞获取接口访问成功
//				QihooAll qihooalls = new QihooAll()
//				qihooalls.dateCreated = new Date()
//				qihooalls.num = t1
//				qihooalls.status = "success"
//				qihooalls.save(flush:true)
            } catch (Exception e) {
                e.printStackTrace()
                //获取漏洞信息接口出错
                QihooAll qihooalls = new QihooAll()
                qihooalls.dateCreated = new Date()
                qihooalls.num = t1
                qihooalls.status = "worning,接口访问成功，但数据有误，请联系接口提供人!"
                qihooalls.save(flush: true)
                return;
            }
            Object[] strs = jsonArray.toArray();
            if (strs.size() > 0) {
                //漏洞获取接口访问成功
                QihooAll qihooalls = new QihooAll()
                qihooalls.dateCreated = new Date()
                qihooalls.num = t1
                qihooalls.status = "success,size="+strs.length+",startTimeStr="+startTimeStr
                qihooalls.save(flush: true)
                //360用户
                //TUser user = TUser.findByEmail("<EMAIL>")
                // 2018-04-26 17:24:30 更改成了  <EMAIL>
                TUser user = TUser.get(9395)
                def filePath = "${grailsApplication.config.filePath.flawAttFilePath}"
                // 如果目录不存在，则创建
                File flawPathFile = new File(filePath);
                if (!flawPathFile.exists()) {
                    flawPathFile.mkdirs()
                }
                int index=1;
                int size=strs.size();
                for (net.sf.json.JSONObject s : strs) {
                    long startTime = System.currentTimeMillis();//获取开始时间
                    //奇虎编号
                    String vId;
                    //单条入库日志
                    QihooOne qihooone = new QihooOne()
                    qihooone.num = t1
                    qihooone.dateCreated = new Date()
                    try {
                        vId = s.getString("vulid");
                    } catch (Exception e) {
                        e.printStackTrace()
                        qihooone.qihooId = vId
                        qihooone.statuOne = "error,奇虎漏洞数据有误，字段无法对应，请联系管理员"
                        qihooone.save(flush: true)
                        index++;
                        continue
                    }
                    try {
                        //判断只有含有漏洞qihooId的才是正常数据
                        if (vId != null && !"".equals(vId)) {
                            //漏洞标题
                            String title = s.getString("title");
                            Date openTime = null;
                            if (s.getString("create_time") == null) {
                                openTime = new Date()
                            } else {
                                openTime = formatter.parse(s.getString("create_time"))
                            }

                            //对数据进行处理
//							def qihooApiDataInstance = new QihooApi()
                            def qihooApiDataInstance = QihooApi.findByQihooId(vId)
                            if (qihooApiDataInstance) {
                                qihooApiDataInstance.status = 0
                                qihooApiDataInstance.save(flush: true)
                                qihooone.qihooId = vId
                                qihooone.statuOne = "warn,数据库含有该漏洞,故不录入,奇虎漏洞编号为" + vId
                                qihooone.foundtime = openTime
                                qihooone.save(flush: true)
                                index++;
                                continue
                            } else {
                                qihooApiDataInstance = new QihooApi()
                                qihooApiDataInstance.qihooId = vId
                            }


                            def flawInstance = Flaw.find("from Flaw t where t.user=:user and t.title=:title and t.enable=1", [user: user, title: title])
                            if (flawInstance) {
                                qihooApiDataInstance.status = 0
                                qihooApiDataInstance.msg = '漏洞标题重复'
                                qihooApiDataInstance.save(flush: true)
                                qihooone.qihooId = vId
                                qihooone.foundtime = openTime
                                qihooone.statuOne = "warn,数据库含有该漏洞,故不录入,奇虎漏洞编号为" + vId
                                qihooone.save(flush: true)
                                index++;
                                continue
                            }
                            flawInstance = new Flaw()
                            def wordUtil = new WordUtil()
                            //漏洞描述
                            String description = s.getString("description")
                            //按照老版本接口规定，如果漏洞描述为空，则不录入该漏洞
                            if (description == null || description.equals("")) {
                                qihooApiDataInstance.status = 0
                                qihooApiDataInstance.msg = '漏洞描述为空'
                                qihooApiDataInstance.save(flush: true)
                                index++;
                                continue;
                            } else {
                                //如果描述不为空  ，保存
                                //漏洞详情，里面含有可下载的图片等附件
                                String detail = s.getString("detail");
                                def patternStr = "<img\\ssrc=\"(.+?)\">";
                                def d = detail.replaceAll(patternStr, "")
                                def attachmentPath = wordUtil.saveQihooAttachment(detail)
                                if (attachmentPath != null) {
                                    File file = new File(attachmentPath)
                                    def fileName = file.getName()
                                    def toFilePath = filePath + fileName
                                    File toFile = new File(toFilePath)
                                    FileCopyUtils.copy(file, toFile);
                                    def attachment = new Attachment()
                                    attachment.realName = fileName
                                    attachment.fileName = fileName
                                    attachment.fileType = "application/msword"
                                    attachment.fileSize = getFileSize(toFile)
                                    attachment.path = toFilePath
                                    attachment.save(flush: true)
                                    flawInstance.attachment = attachment
                                }
                                flawInstance.title = title
                                def detailInfo = new DetailedInfo()
                                //cnvd库中的漏洞描述=360漏洞描述+360漏洞详情
                                detailInfo.description = description + d
                                flawInstance.detailedInfo = detailInfo
                                /*Date openTime = null;
                                if (s.getString("create_time") == null) {
                                    openTime = new Date()
                                } else {
                                    openTime = formatter.parse(s.getString("create_time"))
                                }*/
                                def date = new Date()
                                flawInstance.user = user
                                flawInstance.foundTime = openTime
                                flawInstance.submitTime = date
                                flawInstance.openTime = openTime
                                flawInstance.isAttShow = 0
                                flawInstance.isOpen = 0
                                flawInstance.isZero = 0
                                flawInstance.isOriginal = 0
                                flawInstance.isEvent = 1
                                //备注：新版接口中，是否为事件型初始为事件型、参考链接  两个字段  放入二级审核
                                flawInstance.status = 2
                                // 第二次审核的时间
                                flawInstance.firstTime = new Date()
                                flawInstance.secondTime = new Date()
                                if (!flawInstance.save(flush: true)) {
                                    flawInstance.errors.allErrors.each { println it }
                                } else {
                                    //增加分组父节点--20191104
                                    flawInstance.parentId=flawInstance.id
                                    //提取url
                                    String text = d + "," + detail
                                    //提取url
                                    // url正则
                                    String regex2 = "((https|http|ftp|rtsp|mms)?:\\/\\/([\\w-]+\\.)+[\\w-]+([\\w-./:?%&*=]*))";
                                    Matcher m7 = Pattern.compile(regex2, Pattern.CASE_INSENSITIVE).matcher(text);
                                    while (m7.find()) {
                                        FlawUrl fu = FlawUrl.findByUrlAndFlaw(m7.group().trim(), flawInstance)
                                        if (!fu) {
                                            FlawUrl flawUrl = new FlawUrl()
                                            flawUrl.flaw = flawInstance
                                            flawUrl.url = m7.group().trim()
                                            if (!flawUrl.save(flush: true)) {
                                                flawUrl.errors.allErrors.each { println it }
                                            }
                                            //保存漏洞url到redis
                                            flawService.setFlawUrl(flawInstance.getId(),m7.group().trim())
                                        }
                                    }
                                    System.out.println("入库成功|漏洞id="+flawInstance.id+"|size="+size+"|index="+index+"|漏洞入库时间="+(System.currentTimeMillis()-startTime));
                                    index++;
                                }
                                qihooApiDataInstance.status = 1
                                qihooApiDataInstance.flaw = flawInstance
                                if (!qihooApiDataInstance.save(flush: true)) {
                                    qihooone.qihooId = vId
                                    qihooone.statuOne = "保存失败"
                                    qihooone.foundtime = openTime
                                    qihooone.save(flush: true)
                                    qihooApiDataInstance.errors.allErrors.each { println it }
                                    index++;
                                    continue
                                }
                                //保存单条漏洞录入日志
                                qihooone.qihooId = vId
                                qihooone.statuOne = "success"
                                qihooone.foundtime = openTime
                                qihooone.save(flush: true)
                            }
                        } else {
                            qihooone.qihooId = ""
                            qihooone.statuOne = "error,奇虎漏洞数据有误，该批次的数据不符合录入规则"
                            qihooone.save(flush: true)
                            index++;
                            continue
                        }
                    } catch (Exception e) {
                        e.printStackTrace()
                        qihooone.qihooId = vId
                        qihooone.statuOne = "error,漏洞入库出现错误,奇虎漏洞编号为为" + vId
                        qihooone.save(flush: true)
                        index++;
                        continue
                    }
                }
            } else {
                //漏洞获取接口访问成功
                QihooAll qihooalls = new QihooAll()
                qihooalls.dateCreated = new Date()
                qihooalls.num = t1
                qihooalls.status = "worning,接口访问成功，但数据有误，请联系接口提供人!"
                qihooalls.save(flush: true)
            }
        } else {
            QihooAll qihooalls = new QihooAll()
            qihooalls.dateCreated = new Date()
            qihooalls.num = t1
            qihooalls.status = "error," + json.getString("msg")
            qihooalls.save(flush: true)
            switch (code) {
                case "1001": println "非法请求" + ",  请求时间" + formatter.format(new Date()); break;
                case "1002": println "请求超时" + ",  请求时间" + formatter.format(new Date()); break;
                case "1003": println "请求参数错误" + ",  请求时间" + formatter.format(new Date()); break;
                case "1004": println "漏洞编码错误" + ",  请求时间" + formatter.format(new Date()); break;
                case "1005": println "签名错误" + ",  请求时间" + formatter.format(new Date()); break;
                case "1006": println "无权限" + ",  请求时间" + formatter.format(new Date()); break;
                case "1007": println "漏洞不存在" + ",  请求时间" + formatter.format(new Date()); break;
                default: break;
            }
        }
        println "job finish time = " + formatter.format(new Date())


    }

    def long getFileSize(File file) {
        long s = 0;
        if (file.exists()) {
            FileInputStream fis = null;
            fis = new FileInputStream(file);
            s = fis.available();
        }
        return s;
    }
}