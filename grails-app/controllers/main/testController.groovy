package main

import com.cnvd.*
import com.cnvd.exploit.ExploitTemp
import com.cnvd.exploit.ExploitTrue
import com.cnvd.flawInfo.*
import com.cnvd.industryLibrary.*
import com.cnvd.patchInfo.PatchInfo
import com.cnvd.productInfo.Manufacturer
import com.cnvd.productInfo.ProductInfo
import com.cnvd.util.SQLUtil
import jxl.Workbook
import jxl.WorkbookSettings
import jxl.write.*
import org.codehaus.groovy.grails.commons.ConfigurationHolder
import org.jsoup.Jsoup
import org.jsoup.nodes.Document
import org.jsoup.nodes.Element
import org.jsoup.select.Elements

import java.text.SimpleDateFormat
import java.util.regex.Matcher
import java.util.regex.Pattern

//import org.springframework.util.FileCopyUtils

//4

class testController {
    def static config = ConfigurationHolder.config
    def static SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
    def static SimpleDateFormat sdf = new SimpleDateFormat("yyyy")
    // 伪装火狐浏览器
    private static final String USERAGENT = "Mozilla/5.0 (Windows NT 6.1; rv:38.0) Gecko/20100101 Firefox/38.0";
    // 超时连接时间四分钟
    private static final int TIMEOUT = 120000;
    def flawService

    def nipctest = {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        println "NipcJob start！" + dateFormat.format(date);

        String spec = "http://www.nipc.org.cn/vullist.aspx";
        org.jsoup.nodes.Document doc = null;
        java.lang.Boolean flag = true;
        try {
            doc = Jsoup.connect(spec).userAgent(USERAGENT).timeout(TIMEOUT).get();
        } catch (IOException e1) {
            println "NipcJob Jsoup链接错误！|连接地址spec：" + spec + "|连接时间：" + dateFormat.format(new Date());
            return;
        }

        Element ets = doc.getElementsByClass("panel-body").first();
        Elements etsdiv = ets.getElementsByTag("td").select("a[href]");
        String url = "";
        for (int i = 0; i < etsdiv.size(); i = i + 3) {
            url = etsdiv[i].getAllElements().attr("href");
            String spec2 = "http://www.nipc.org.cn/" + url;
            org.jsoup.nodes.Document doc1 = null;
            try {
                doc1 = Jsoup.connect(spec2).userAgent(USERAGENT).timeout(
                        TIMEOUT).get();
            } catch (IOException e) {
                e.printStackTrace();
                println "NipcJob 网站连接失败！|连接地址spec：" + spec + "|连接时间：" + dateFormat.format(new Date());
                continue;
            }
            java.util.Date submitTime;
            java.util.Date updatedTime;
            def threadId;
            org.jsoup.nodes.Element wrap = doc1.getElementsByClass("middle").first();
            String title = wrap.getElementById("ContentPlaceHolder1_LabCncveName").text();
            //漏洞标题
            String bugNumber = wrap.getElementById("ContentPlaceHolder1_LabCncveID").text();
            //漏洞编号
            String threadStr = wrap.getElementById("ContentPlaceHolder1_LabOutSeverity").text().trim();
            //危险等级
            if (threadStr.equals("紧急")) {
                threadId = 18;
            } else if (threadStr.equals("高")) {
                threadId = 19;
            } else if (threadStr.equals("中")) {
                threadId = 20;
            } else if (threadStr.equals("低")) {
                threadId = 20;
            } else {
                threadId = 17;    //未知
            }
            String detailedInfo = wrap.getElementById("ContentPlaceHolder1_LabVulDescrible").text();
            //漏洞描述
            String formalWay = wrap.getElementById("ContentPlaceHolder1_LabSolution").text();
            //解决方案
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            try {
                String submitTimeStr = wrap.getElementById("ContentPlaceHolder1_LabPublishTime").text().trim();
                //发布日期
                submitTime = sdf.parse(submitTimeStr);
                String updatedTimeStr = wrap.getElementById("ContentPlaceHolder1_LabUpDateTime").text().trim();
                //更新日期
                updatedTime = sdf.parse(updatedTimeStr);
            } catch (Exception e) {
                e.printStackTrace()
            }
            //查询CNVD工作组Tuser
            def cnvdUser = TUser.get(1);
            //漏洞表
            Flaw flawInstanceList = new Flaw();
            //漏洞详细信息
            DetailedInfo detailedInfoList = new DetailedInfo();
            //爬取网站记录表
            WebLog webLog = new WebLog();

            //通过weblog中的记录验证本条数据是否已经存储
            def webLogList = WebLog.findByNumber(bugNumber)
            if (!webLogList) {
                //保存
                flawInstanceList.fromWhere = "crawler_NipcJob";    //数据来源
                flawInstanceList.dateCreated = new Date();    //录入时间(不能为空)
                flawInstanceList.enable = 1; //1可用(不能为空)
                flawInstanceList.foundTime = submitTime;      //发现时间(不能为空)
                flawInstanceList.submitTime = submitTime; //漏洞报送时间 (不能为空)
                flawInstanceList.lastUpdated = updatedTime;    ////更新日期(不能为空)
                flawInstanceList.status = 4;    //此状态表示将数据放入补录列表中(不能为空)
                flawInstanceList.title = title;    //漏洞标题(不能为空)
                flawInstanceList.user = cnvdUser;    //漏洞录入人
                flawInstanceList.serverityId = threadId;    //漏洞威胁大小	ID 高18  中19 低20 未知17
                flawInstanceList.isAdditional = 1; //1为补录漏洞

                detailedInfoList.description = detailedInfo;    //漏洞描述
                detailedInfoList.formalWay = formalWay;        //正式解决办法（建议）
                detailedInfoList.fromWhere = "crawler_NipcJob";    //数据来源
                flawInstanceList.detailedInfo = detailedInfoList;
                if (!flawInstanceList.save(flush: true)) {
                    flawInstanceList.errors.allErrors.each { println it }
                }
                webLog.number = bugNumber;
                webLog.webnum = "nipc";
                webLog.submitDate = new Date();
                webLog.save(flush: true);
            }

        }
        println "NipcJob end！" + dateFormat.format(new Date());
    }


    def cnnvdtest = {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        println "CnnvdJob start！" + dateFormat.format(date);

        String spec = "http://www.cnnvd.org.cn/web/vulnerability/querylist.tag";
        org.jsoup.nodes.Document doc = null;
        try {
            System.setProperty("https.protocols", "TLSv1.2,TLSv1.1,SSLv3");
            doc = Jsoup.connect(spec).userAgent(USERAGENT).timeout(TIMEOUT).get();
        } catch (IOException e1) {
            e1.printStackTrace();
            println "CnnvdJob 网站连接失败！" + dateFormat.format(new Date());
            return;
        }
        org.jsoup.nodes.Element wrap = doc.getElementsByClass("list_list").first();
        Elements wrap1 = wrap.getElementsByTag("p").select("a[href]");
        String url = "";
        for (int i = 0; i < wrap1.size(); i = i + 2) {
            url = wrap1[i].getAllElements().attr("href");
            String spec1 = "http://www.cnnvd.org.cn" + url;

            org.jsoup.nodes.Document doc1 = null;
            try {
                System.setProperty("https.protocols", "TLSv1.2,TLSv1.1,SSLv3");
                doc1 = Jsoup.connect(spec1).userAgent(USERAGENT).timeout(
                        TIMEOUT).get();
            } catch (IOException e) {
                e.printStackTrace();
                continue;
            }


            String title = doc1.getElementsByClass("detail_xq").first().getElementsByTag("h2").get(0).text();
            //标题
            String cnnvdNumber = doc1.getElementsByClass("detail_xq").first().getElementsByTag("span").get(0).text().replaceAll("CNNVD编号：", "");
            //cnnvd编号
            //发布时间
            String submitTimeStr = doc1.getElementsByClass("detail_xq").first().getElementsByTag("li").get(4).getElementsByTag("a").get(0).text();
            //更新时间
            String updatedTimeStr = doc1.getElementsByClass("detail_xq").first().getElementsByTag("li").get(6).getElementsByTag("a").get(0).text();
            //危害等级
            String threadStr = doc1.getElementsByClass("detail_xq").first().getElementsByTag("li").get(1).getElementsByTag("a").get(0).text();
            //漏洞描述
            String detailedInfo = "";
            def detailedInfotag = doc1.getElementsByClass("d_ldjj").first().getElementsByTag("p")
            for (int j = 0; j < detailedInfotag.size(); j = j + 1) {
                detailedInfo += detailedInfotag.get(j).text()
            }
            //正式解决方案
            String formalWay = "";
            def formalWaytag = doc1.getElementsByClass("d_ldjj").get(1).getElementsByTag("p")
            for (int j = 0; j < formalWaytag.size(); j = j + 1) {
                formalWay += formalWaytag.get(j).text()
            }
            java.util.Date submitTime;
            java.util.Date updatedTime;
            def threadId;
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            try {
                submitTime = sdf.parse(submitTimeStr);
                updatedTime = sdf.parse(updatedTimeStr);
            } catch (Exception e) {
                e.printStackTrace()
            }
            if (threadStr.equals("危急")) {
                threadId = 18;
            } else if (threadStr.equals("高危")) {
                threadId = 19;
            } else if (threadStr.equals("中危")) {
                threadId = 20;
            } else if (threadStr.equals("低位")) {
                threadId = 20;
            } else {
                threadId = 17;    //未知
            }
            //cve
            String referenceNumber = null;
            String linkUrl = null;
            try {
                referenceNumber = doc1.getElementsByClass("detail_xq").first().getElementsByTag("li").get(2).getElementsByTag("a").get(0).text();
                linkUrl = doc1.getElementsByClass("detail_xq").first().getElementsByTag("li").get(2).select("a[href]").attr("href");
            } catch (Exception e) {
                e.printStackTrace()
                continue;
            }
            if (referenceNumber != "" && referenceNumber != null) {
                //判断CVE是否存在，存在则不录入
                def referenceInfolist = ReferenceInfo.findByReferenceNumber(referenceNumber);
                if (referenceInfolist) {
                    continue;
                }
            }
            //查询CNVD工作组Tuser
            def cnvdUser = TUser.get(1);
            //漏洞表
            Flaw flawInstanceList = new Flaw();
            //漏洞详细信息
            DetailedInfo detailedInfoList = new DetailedInfo();
            //爬取网站记录表
            WebLog webLog = new WebLog();

            //通过weblog中的记录验证本条数据是否已经存储
            def webLogList = WebLog.findByNumber(cnnvdNumber)
            if (!webLogList) {
                //保存
                flawInstanceList.fromWhere = "crawler_CnnvdJob";    //数据来源
                flawInstanceList.dateCreated = new Date();    //录入时间(不能为空)
                flawInstanceList.enable = 1; //1可用(不能为空)
                flawInstanceList.foundTime = submitTime;      //发现时间(不能为空)
                flawInstanceList.submitTime = submitTime; //漏洞报送时间 (不能为空)
                flawInstanceList.lastUpdated = updatedTime;    ////更新日期(不能为空)
                flawInstanceList.status = 4;    //此状态表示将数据放入补录列表中(不能为空)
                flawInstanceList.title = title;    //漏洞标题(不能为空)
                flawInstanceList.user = cnvdUser;    //漏洞录入人
                flawInstanceList.serverityId = threadId;    //漏洞威胁大小	ID 高18  中19 低20 未知17
                flawInstanceList.isAdditional = 1; //1为补录漏洞

                detailedInfoList.description = detailedInfo;    //漏洞描述
                detailedInfoList.formalWay = formalWay;        //正式解决办法（建议）
                detailedInfoList.fromWhere = "crawler_CnnvdJob";    //数据来源
                flawInstanceList.detailedInfo = detailedInfoList;
                if (!flawInstanceList.save(flush: true)) {
                    flawInstanceList.errors.allErrors.each { println it }
                }

                //外部引用入库
                def flawList = Flaw.findByTitle(title);
                ReferenceInfo referenceInfo = new ReferenceInfo();
                ReferenceType referenceType = new ReferenceType();
                def referenceTypeList = ReferenceType.findByName("CVE");
                referenceInfo.referenceType = referenceTypeList;
                referenceInfo.flaw = flawList;
                referenceInfo.referenceNumber = referenceNumber;
                referenceInfo.linkUrl = linkUrl;
                referenceInfo.fromWhere = "crawler_Cnnvd";    //数据来源

                if (!referenceInfo.save(flush: true)) {
                    referenceInfo.errors.allErrors.each { println it }
                    println "外部引用错误！"
                }

                webLog.number = cnnvdNumber;
                webLog.webnum = "cnnvd";
                webLog.submitDate = new Date();
                webLog.save(flush: true);
            }
        }
        println "CnnvdJob end！" + dateFormat.format(new Date());
    }


    def zerodayinitiativetest = {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        println "ZerodayinitiativeJob start！" + dateFormat.format(date);

        //—————————————爬取部分—————————————
        String spec = "https://www.zerodayinitiative.com/advisories/published/";
        org.jsoup.nodes.Document doc = null;
        try {
            System.setProperty("https.protocols", "TLSv1.2,TLSv1.1,SSLv3");
            doc = Jsoup.connect(spec).userAgent(USERAGENT).timeout(TIMEOUT).get();
        } catch (IOException e1) {
            e1.printStackTrace();
            println "ZerodayinitiativeJob连接网站失败！" + dateFormat.format(new Date());
            return;
        }
        org.jsoup.nodes.Element wrap = doc.getElementById("case-table");
        //抽取时间。用于检验是否存在
        String theTime = wrap.getElementsByTag("tr").get(0).getElementsByTag("td").get(2).text().replaceAll("Published:", "").trim();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");


        Elements wrap1 = wrap.select("a[href]");
        String url = "";
        int wrapSize = wrap1.size() - 1;
        for (int i = 0; i < wrap1.size() - 1; i++) {
            url = wrap1[i].getAllElements().attr("href");
            String spec1 = "https://www.zerodayinitiative.com" + url + "/";
//			System.out.println(spec1);
            org.jsoup.nodes.Document doc1 = null;
            try {
                System.setProperty("https.protocols", "TLSv1.2,TLSv1.1,SSLv3");
                doc1 = Jsoup.connect(spec1).userAgent(USERAGENT).timeout(
                        TIMEOUT).get();
            } catch (IOException e) {
                e.printStackTrace();
                continue;
            }

            org.jsoup.nodes.Element wrap2 = doc1.getElementById("main-content");
            //用于截取处理
            String wrapText = wrap2.text();
            //本网站编号
            String webbakstr = wrap2.getElementsByTag("b").text().trim();
            //漏洞标题(翻译)
            String titleEN = wrap2.getElementsByTag("h2").text();
            //漏洞描述(翻译)
            String detailsEN = wrap2.getElementsByTag("p").text();

            int first;
            int last;
            String formalWayEN;
            String changShang;
            String cveNum;
            String cveLink;
            String timeStr;

            try {
                //解决方案(翻译)
                first = wrapText.indexOf("Vendor Response");
                last = wrapText.indexOf("Disclosure Timeline");
                formalWayEN = wrapText.substring(first + 15, last);
                //厂商
                changShang = wrap2.getElementsByTag("a").get(2).text();
                //外部引用CVE
                int referenceType_id_cve = 1;    //默认1位CVE
                cveNum = wrap2.getElementsByTag("a").get(0).text();    //CVE编号
                if (cveNum != null && cveNum != "") {
                    def referenceInfolist = ReferenceInfo.findByReferenceNumber(cveNum);
                    if (referenceInfolist) {
                        continue;
                    }
                }
                cveLink = wrap2.getElementsByTag("a").get(0).attr("href");
                //时间截取
                timeStr = wrap.getElementsByTag("tr")[i * 3].getElementsByTag("td").get(2).text();
            } catch (Exception e) {
//				e.printStackTrace()
                continue;
            }
            //漏洞发现时间
            String foundTimeStr = timeStr.replaceAll("Published:", "").trim();
            //漏洞报送时间
            String submitTimeStr = foundTimeStr;
            //漏洞更新时间
            String updateTimeStr = foundTimeStr;
            Date foundTime = null;
            Date submitTime = null;
            Date updateTime = null;
            try {
                foundTime = sdf.parse(foundTimeStr);
                submitTime = sdf.parse(submitTimeStr);
                updateTime = sdf.parse(updateTimeStr);
            } catch (Exception e) {
                e.printStackTrace()
            }
            //—————————————翻译部分—————————————
            def title;
            def details;
            def formalWay;
            try {
                title = BaiduTranslate.getTransResult(titleEN, "en", "zh");        //翻译标题
                details = BaiduTranslate.getTransResult(detailsEN, "en", "zh");    //翻译描述
                formalWay = BaiduTranslate.getTransResult(formalWayEN, "en", "zh");        //翻译解决方法
            } catch (Exception e) {
                e.printStackTrace();
                System.out.println("翻译出错，参考百度错误代码和说明。");
                return;
            }
            //—————————————入库部分—————————————
            //查询CNVD工作组Tuser
            def cnvdUser = TUser.get(1);
            //漏洞表
            Flaw flawInstanceList = new Flaw();
            //漏洞详细信息
            DetailedInfo detailedInfoList = new DetailedInfo();
            //爬取网站记录表
            WebLog webLog = new WebLog();
            //外部引用
            ReferenceInfo referenceInfo = new ReferenceInfo();
            ReferenceType referenceType = new ReferenceType();
            //厂商表
            Manufacturer manufacturer = new Manufacturer();
            //通过weblog中的记录验证本条数据是否已经存储
            def webLogList = WebLog.findByNumber(webbakstr)
            if (!webLogList) {
                //保存
                flawInstanceList.fromWhere = "crawler_ZerodayinitiativeJob";    //数据来源
                flawInstanceList.dateCreated = new Date();    //录入时间(不能为空)
                flawInstanceList.enable = 1; //1可用(不能为空)
                flawInstanceList.foundTime = foundTime;      //发现时间(不能为空)
                flawInstanceList.submitTime = submitTime; //漏洞报送时间 (不能为空)
                flawInstanceList.lastUpdated = updateTime;    ////更新日期(不能为空)
                flawInstanceList.status = 4;    //此状态表示将数据放入补录列表中(不能为空)
                flawInstanceList.title = title;    //漏洞标题(不能为空)
                flawInstanceList.user = cnvdUser;    //漏洞录入人
                flawInstanceList.serverityId = 17;    //漏洞威胁大小	ID 高18  中19 低20 未知17
                flawInstanceList.isAdditional = 1; //1为补录漏洞

                detailedInfoList.description = details;    //漏洞描述
                detailedInfoList.formalWay = formalWay;        //正式解决办法（建议）
                detailedInfoList.fromWhere = "crawler_ZerodayinitiativeJob";    //数据来源

                //厂商
                def manufacturerList = Manufacturer.findByName(changShang)
                if (!manufacturerList) {
                    manufacturer.name = changShang;    //厂商名称(不能为空)
                    manufacturer.dateCreated = new Date();    //创建日期(不能为空)
                    manufacturer.lastUpdated = new Date();    //更新日期(不能为空)
                    manufacturer.fromWhere = "crawler_ZerodayinitiativeJob";    //数据来源
                    if (!manufacturer.save(flush: true)) {
                        manufacturer.errors.allErrors.each { println it }
                    }
                    def manufacturerLi = Manufacturer.findByName(changShang)
                    flawInstanceList.manufacturer = manufacturerLi;
                } else {
                    flawInstanceList.manufacturer = manufacturerList;
                }

                flawInstanceList.detailedInfo = detailedInfoList;
                if (!flawInstanceList.save(flush: true)) {
                    flawInstanceList.errors.allErrors.each { println it }
                }
                String s1 = "CVE";
                boolean flag = cveNum.contains(s1);
                if (flag == true) {
                    //外部引用入库
                    def flawList = Flaw.findByTitle(title);
                    def referenceTypeList = ReferenceType.findByName("CVE");
                    referenceInfo.referenceType = referenceTypeList;
                    referenceInfo.flaw = flawList;
                    referenceInfo.referenceNumber = cveNum;
                    referenceInfo.linkUrl = cveLink;
                    referenceInfo.fromWhere = "crawler_ZerodayinitiativeJob";    //数据来源
                    if (!referenceInfo.save(flush: true)) {
                        referenceInfo.errors.allErrors.each { println it }
                        println "外部引用！"
                    }
                } else {
                    //外部引用入库
                    def flawList = Flaw.findByTitle(title);
                    def referenceTypeList = ReferenceType.findByName("其他");
                    referenceInfo.referenceType = referenceTypeList;
                    referenceInfo.flaw = flawList;
                    referenceInfo.referenceNumber = cveNum;
                    referenceInfo.linkUrl = cveLink;
                    referenceInfo.fromWhere = "crawler_ZerodayinitiativeJob";    //数据来源
                    if (!referenceInfo.save(flush: true)) {
                        referenceInfo.errors.allErrors.each { println it }
                        println "外部引用！"
                    }

                }
                webLog.number = webbakstr;
                webLog.webnum = "Zerodayinitiative";
                webLog.submitDate = new Date();
                webLog.save(flush: true);
            }
        }
        println "ZerodayinitiativeJob end！" + dateFormat.format(new Date());
    }
    def securityfocustest = {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        println "SecurityfocusJob start！" + dateFormat.format(date);

        String spec = "http://www.securityfocus.com/";
        org.jsoup.nodes.Document doc = null;
        try {
            System.setProperty("https.protocols", "TLSv1.2,TLSv1.1,SSLv3");
            doc = Jsoup.connect(spec).userAgent(USERAGENT).timeout(TIMEOUT).get();
        } catch (IOException e1) {
            e1.printStackTrace();
        }
        org.jsoup.nodes.Element wrap = doc.select("table").get(2);
        Elements wrapHref = wrap.select("a[href]");
        String url = "";

        for (int i = 0; i < wrapHref.size(); i = i + 2) {
            url = wrapHref[i].getAllElements().attr("href");
            String spec1 = "https://www.securityfocus.com" + url;
            String spec2 = "https://www.securityfocus.com" + url + "/discuss";    //获取描述信息的URL
            String spec3 = "https://www.securityfocus.com" + url + "/solution";        //获取解决方案的URL

            org.jsoup.nodes.Document doc1 = null;
            org.jsoup.nodes.Document doc2 = null;
            org.jsoup.nodes.Document doc3 = null;
            try {
                System.setProperty("https.protocols", "TLSv1.2,TLSv1.1,SSLv3");
                doc1 = Jsoup.connect(spec1).userAgent(USERAGENT).timeout(TIMEOUT).get();
                doc2 = Jsoup.connect(spec2).userAgent(USERAGENT).timeout(TIMEOUT).get();
                doc3 = Jsoup.connect(spec3).userAgent(USERAGENT).timeout(TIMEOUT).get();
            } catch (IOException e) {
                e.printStackTrace();
                continue;
            }
            org.jsoup.nodes.Element docEl = doc1.getElementById("vulnerability");
            org.jsoup.nodes.Element docEl2 = doc2.getElementById("vulnerability");
            org.jsoup.nodes.Element docEl3 = doc3.getElementById("vulnerability");

            String miaoshuEN = docEl2.text();        //漏洞的描述，需要翻译
            String solutionEN = docEl3.text();    //漏洞的解决方法，需要翻译

            String titleEN = docEl.getElementsByClass("title").text();    //漏洞标题，需要翻译
            String bugtraqId = docEl.select("tr").get(0).select("td").get(1).text().trim();
            //bugtraqId
            String cveNum = docEl.select("tr").get(2).select("td").get(1).text();    //cveid
            if (cveNum != null && cveNum != "") {
                def referenceInfolist = ReferenceInfo.findByReferenceNumber(cveNum);
                if (referenceInfolist) {
                    continue;
                }
            }
            String published = docEl.select("tr").get(5).select("td").get(1).text().trim();
            //提交时间
            String updated = docEl.select("tr").get(6).select("td").get(1).text().trim();    //提交时间
            SimpleDateFormat sdf = new SimpleDateFormat("MMM dd yyyy HH:mma", Locale.US);//日期类型转换
            Date publishedDate;
            Date updatedDate;
            try {
                publishedDate = sdf.parse(published);
                updatedDate = sdf.parse(updated);
            } catch (Exception e) {
                e.printStackTrace()
            }

            String CreditEN = docEl.select("tr").get(7).select("td").get(1).text();    //漏洞提供商
            def titleCH;
            def miaoshuCH;
            def solutionCH;
            //调用百度翻译Api翻译
            try {
                titleCH = BaiduTranslate.getTransResult(titleEN, "en", "zh");        //翻译标题
                miaoshuCH = BaiduTranslate.getTransResult(miaoshuEN, "en", "zh");    //翻译描述
                solutionCH = BaiduTranslate.getTransResult(solutionEN, "en", "zh");        //翻译解决方法
            } catch (Exception e) {
//				e.printStackTrace();
                System.out.println("翻译出错，job为Securityfocusjob,参考百度错误代码和说明。");
            }

            //将数据入库
            //查询CNVD工作组Tuser
            def cnvdUser = TUser.get(1);
            //漏洞表
            Flaw flawInstanceList = new Flaw();
            //漏洞详细信息
            DetailedInfo detailedInfoList = new DetailedInfo();
            //爬取网站记录表
            WebLog webLog = new WebLog();
            //外部引用
            ReferenceInfo referenceInfo = new ReferenceInfo();
            ReferenceType referenceType = new ReferenceType();

            //通过weblog中的记录验证本条数据是否已经存储
            def webLogList = WebLog.findByNumber(bugtraqId)
            if (!webLogList) {
//				println titleCH;
                //保存
                flawInstanceList.fromWhere = "crawler_SecurityfocusJob";    //数据来源
                flawInstanceList.dateCreated = new Date();    //录入时间(不能为空)
                flawInstanceList.enable = 1; //1可用(不能为空)
                flawInstanceList.foundTime = publishedDate;      //发现时间(不能为空)
                flawInstanceList.submitTime = publishedDate; //漏洞报送时间 (不能为空)
                flawInstanceList.lastUpdated = updatedDate;    ////更新日期(不能为空)
                flawInstanceList.status = 4;    //此状态表示将数据放入补录列表中(不能为空)
                flawInstanceList.title = titleCH;    //漏洞标题(不能为空)
                flawInstanceList.user = cnvdUser;    //漏洞录入人
//				flawInstanceList.serverityId = threadId;	//漏洞威胁大小	ID 高18  中19 低20 未知17
                flawInstanceList.isAdditional = 1; //1为补录漏洞

                detailedInfoList.description = miaoshuCH;    //漏洞描述
                detailedInfoList.formalWay = solutionCH;        //正式解决办法（建议）
                detailedInfoList.fromWhere = "crawler_SecurityfocusJob";    //数据来源
                flawInstanceList.detailedInfo = detailedInfoList;
                if (!flawInstanceList.save(flush: true)) {
                    flawInstanceList.errors.allErrors.each { println it }
                }
                //外部引用入库
                def flawList = Flaw.findByTitle(titleCH);
                def referenceTypeList = ReferenceType.findByName("CVE");
                referenceInfo.referenceType = referenceTypeList;
                referenceInfo.flaw = flawList;
                referenceInfo.referenceNumber = cveNum;
                referenceInfo.fromWhere = "crawler_SecurityfocusJob";    //数据来源

                if (!referenceInfo.save(flush: true)) {
                    referenceInfo.errors.allErrors.each { println it }
                    println "外部引用！"
                }
                webLog.number = bugtraqId;
                webLog.webnum = "securityfocus";
                webLog.submitDate = new Date();
                webLog.save(flush: true);
            }
        }
        println "SecurityfocusJob end！" + dateFormat.format(new Date());

    }


    def nvdtest = {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        println "NvdJob start！" + dateFormat.format(date);

        //—————————————爬取部分—————————————
        String spec = "https://web.nvd.nist.gov/view/vuln/search-results";
        Document doc = null;
        try {
            System.setProperty("https.protocols", "TLSv1.2,TLSv1.1,SSLv3");
            doc = Jsoup.connect(spec).userAgent(USERAGENT).timeout(TIMEOUT).get();
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        def wrap = doc.getElementsByClass("table").first();
        def wrap1 = wrap.getElementsByTag("th").select("a[href]");
        String url = "";
        for (int i = 0; i < wrap1.size(); i++) {
            //			println "___________________"+i
            url = wrap1[i].getAllElements().attr("href");
            String urlStr = "https://web.nvd.nist.gov/" + url;
            Document doc1 = null;
            try {
                System.setProperty("https.protocols", "TLSv1.2,TLSv1.1,SSLv3");
                doc1 = Jsoup.connect(urlStr).userAgent(USERAGENT).timeout(TIMEOUT).get();
            } catch (Exception e) {
                println "无法连接对方网站，单次任务" + urlStr
                continue;
            }
            Element wrap2 = doc1.getElementById("page-content");

            //本网站编号
            String webbakstr = wrap1[i].text().trim();
            //漏洞标题(需翻译)
            String titleEN = webbakstr + " Detail";
            //漏洞描述(需翻译)
            String detailsEN = wrap2.getElementsByTag("p").get(1).text();
            //解决方案(需翻译)
            String formalWayEN = wrap2.getElementsByTag("p").get(3).text();
            //外部引用CVE
            int referenceType_id_cve = 1;    //默认1位CVE
            String cveNum = wrap1[i].text().trim();    //CVE编号entry
            if (cveNum != null && cveNum != "") {
                def referenceInfolist = ReferenceInfo.findByReferenceNumber(cveNum);
                if (referenceInfolist) {
                    continue;
                }
            }
            String cveLink = "http://cve.mitre.org/cgi-bin/cvename.cgi?name=" + cveNum;
            //漏洞发现时间
            String foundTimeStr = wrap2.getElementsByTag("span").get(3).text().trim();
            //漏洞报送时间
            String submitTimeStr = foundTimeStr;
            //漏洞更新时间
            String updateTimeStr = wrap2.getElementsByTag("span").get(4).text().trim();

            SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");
            Date foundTime = null;
            Date submitTime = null;
            Date updateTime = null;
            try {
                foundTime = sdf.parse(foundTimeStr);
                submitTime = sdf.parse(submitTimeStr);
                updateTime = sdf.parse(updateTimeStr);
            } catch (Exception e) {
                e.printStackTrace()
                continue
            }

            //—————————————翻译部分—————————————
            def title;
            def details;
            def formalWay;
            try {
                title = BaiduTranslate.getTransResult(titleEN, "en", "zh");        //翻译标题
                details = BaiduTranslate.getTransResult(detailsEN, "en", "zh");    //翻译描述
                formalWay = BaiduTranslate.getTransResult(formalWayEN, "en", "zh");        //翻译解决方法
            } catch (Exception e) {
                //				e.printStackTrace();
                System.out.println("翻译出错，job为Nvdjob，参考百度错误代码和说明。");
            }
            //—————————————入库部分—————————————
            //查询CNVD工作组Tuser
            def cnvdUser = TUser.get(1);
            //漏洞表
            Flaw flawInstanceList = new Flaw();
            //漏洞详细信息
            DetailedInfo detailedInfoList = new DetailedInfo();
            //爬取网站记录表
            WebLog webLog = new WebLog();
            //外部引用
            ReferenceInfo referenceInfo = new ReferenceInfo();
            ReferenceType referenceType = new ReferenceType();

            //通过weblog中的记录验证本条数据是否已经存储
            def webLogList = WebLog.findByNumber(webbakstr)
            if (!webLogList) {
                //保存
                flawInstanceList.fromWhere = "crawler_NvdJob";    //数据来源
                flawInstanceList.dateCreated = new Date();    //录入时间(不能为空)
                flawInstanceList.enable = 1; //1可用(不能为空)
                flawInstanceList.foundTime = foundTime;      //发现时间(不能为空)
                flawInstanceList.submitTime = submitTime; //漏洞报送时间 (不能为空)
                flawInstanceList.lastUpdated = updateTime;    ////更新日期(不能为空)
                flawInstanceList.status = 4;    //此状态表示将数据放入补录列表中(不能为空)
                flawInstanceList.title = title;    //漏洞标题(不能为空)
                flawInstanceList.user = cnvdUser;    //漏洞录入人
                flawInstanceList.serverityId = 17;    //漏洞威胁大小	ID 高18  中19 低20 未知17
                flawInstanceList.isAdditional = 1; //1为补录漏洞

                detailedInfoList.description = details;    //漏洞描述
                detailedInfoList.formalWay = formalWay;        //正式解决办法（建议）
                detailedInfoList.fromWhere = "crawler_NvdJob";    //数据来源
                flawInstanceList.detailedInfo = detailedInfoList;
                if (!flawInstanceList.save(flush: true)) {
                    flawInstanceList.errors.allErrors.each { println it }
                }
                //外部引用入库
                def flawList = Flaw.findByTitle(title);
                def referenceTypeList = ReferenceType.findByName("CVE");
                referenceInfo.referenceType = referenceTypeList;
                referenceInfo.flaw = flawList;
                referenceInfo.referenceNumber = cveNum;
                referenceInfo.linkUrl = cveLink;
                referenceInfo.fromWhere = "crawler_NvdJob";    //数据来源
                if (!referenceInfo.save(flush: true)) {
                    referenceInfo.errors.allErrors.each { println it }
                    println "外部引用！"
                }
                webLog.number = webbakstr;
                webLog.webnum = "nvd";
                webLog.submitDate = new Date();
                webLog.save(flush: true);
            }

        }
        println "NvdJob end！" + dateFormat.format(new Date());
    }


    def mozillatest = {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        println "MozillaJob start！" + dateFormat.format(date);
        //—————————————爬取部分—————————————
        SimpleDateFormat sdf = new SimpleDateFormat("MMM dd yyyy", Locale.US);//日期类型转换
        String urlString = "https://www.mozilla.org/en-US/security/advisories/";
        org.jsoup.nodes.Document doc = null;
        try {
            System.setProperty("https.protocols", "TLSv1.2,TLSv1.1,SSLv3");
            doc = Jsoup.connect(urlString).userAgent(USERAGENT).timeout(TIMEOUT).get();
        } catch (IOException e1) {
            e1.printStackTrace();
        }
        def articleBody = doc.getElementsByClass("basic-main")
        String timeFlagStr = articleBody.first().getElementsByTag("h2").get(0).text().replaceAll(",", "");
        //校验此时间段之前的数据是否已存在
        Date timeFlag = sdf.parse(timeFlagStr);
        def theFlawList = Flaw.findByFoundTime(timeFlag)
        if (theFlawList == null) {
            Elements wrap = doc.getElementsByClass("basic-main").select("a[href]");
            String url = "";
            for (int i = 0; i < wrap.size(); i++) {
                url = wrap[i].getAllElements().attr("href");
                String urlStr = "https://www.mozilla.org" + url;
                String output1 = new String(HttpsUtil.getMethod(urlStr));
                org.jsoup.nodes.Document doc1 = Jsoup.connect(urlString).userAgent(USERAGENT).timeout(TIMEOUT).get();
                org.jsoup.nodes.Element wrap1 = doc1.getElementsByClass("basic-main").first();

                //本网站编号
                String webbakstr = wrap[i].getElementsByTag("span").text();
                //漏洞标题(翻译)
                String titleEN = wrap1.getElementsByTag("h2").text();
                //漏洞描述(翻译)
                String detailsEN = wrap1.getElementsByTag("p").text();
                //厂商
                String changshang = "mozilla";
                //危害等级
                String threadStrEN = wrap1.getElementsByTag("dd").get(2).text();
                int thread;
                //High Medium Low	ID 高18  中19 低20 未知17
                if (threadStrEN.contains("Critical")) {
                    thread = 18;
                } else if (threadStrEN.contains("High")) {
                    thread = 19;
                } else if (threadStrEN.contains("Moderate")) {
                    thread = 20;
                } else {
                    thread = 20;
                }
                //漏洞发现时间
                String foundTimeStr = wrap1.getElementsByTag("dd").first().text().replace(",", "");
                //漏洞报送时间
                String submitTimeStr = foundTimeStr;
                //漏洞更新时间
                String updateTimeStr = foundTimeStr;
                Date foundTime = null;
                Date submitTime = null;
                Date updateTime = null;
                try {
                    foundTime = sdf.parse(foundTimeStr);
                    submitTime = sdf.parse(submitTimeStr);
                    updateTime = sdf.parse(updateTimeStr);
                } catch (Exception e) {
                    e.printStackTrace()
                }

                //外部引用CVE
                List<Map<String, String>> maps = new ArrayList<Map<String, String>>();
                Elements cveCount = wrap1.getElementsByClass("ex-ref");    //有多少CVE
                for (int k = 0; k < cveCount.size(); k++) {
                    String cveNum = cveCount[k].text();
                    if (cveNum != null && cveNum != "") {
                        def referenceInfolist = ReferenceInfo.findByReferenceNumber(cveNum);
                        if (referenceInfolist) {
                            continue;
                        }
                    }

                    String cveLink = cveCount[k].attr("href");
                    Map<String, String> mapCve = new HashMap<String, String>();
                    mapCve.put("ReferenceTypeName", "CVE");
                    mapCve.put("linkUrl", cveLink);
                    mapCve.put("referenceNumber", cveNum);
                    maps.add(mapCve);
                }

                //—————————————翻译部分—————————————
                def title;
                def details;
                try {
                    title = BaiduTranslateDemo.translate(titleEN, "en", "zh");        //翻译标题
                    details = BaiduTranslateDemo.translate(detailsEN, "en", "zh");    //翻译描述
                } catch (Exception e) {
                    // TODO Auto-generated catch block
//					e.printStackTrace();
                    System.out.println("翻译出错，job为Mozillsjob，参考百度错误代码和说明。");
                    continue;
                }
                //—————————————入库部分—————————————
                //查询CNVD工作组Tuser
                def cnvdUser = TUser.get(1);
                //漏洞表
                Flaw flawInstanceList = new Flaw();
                //漏洞详细信息
                DetailedInfo detailedInfoList = new DetailedInfo();
                //爬取网站记录表
                WebLog webLog = new WebLog();

                //厂商表
                Manufacturer manufacturer = new Manufacturer();
                //通过weblog中的记录验证本条数据是否已经存储
                def webLogList = WebLog.findByNumber(webbakstr)
                if (!webLogList) {
                    //保存
                    flawInstanceList.fromWhere = "crawler_MozillaJob";    //数据来源
                    flawInstanceList.dateCreated = new Date();    //录入时间(不能为空)
                    flawInstanceList.enable = 1; //1可用(不能为空)
                    flawInstanceList.foundTime = foundTime;      //发现时间(不能为空)
                    flawInstanceList.submitTime = submitTime; //漏洞报送时间 (不能为空)
                    flawInstanceList.lastUpdated = updateTime;    ////更新日期(不能为空)
                    flawInstanceList.status = 4;    //此状态表示将数据放入补录列表中(不能为空)
                    flawInstanceList.title = title;    //漏洞标题(不能为空)
                    flawInstanceList.user = cnvdUser;    //漏洞录入人
                    flawInstanceList.serverityId = thread;    //漏洞威胁大小	ID 高18  中19 低20 未知17
                    flawInstanceList.isAdditional = 1; //1为补录漏洞

                    detailedInfoList.description = details;    //漏洞描述
                    detailedInfoList.fromWhere = "crawler_MozillaJob";    //数据来源

                    //厂商
                    def manufacturerList = Manufacturer.findByName(changshang)
                    if (!manufacturerList) {
                        manufacturer.name = changshang;    //厂商名称(不能为空)
                        manufacturer.dateCreated = new Date();    //创建日期(不能为空)
                        manufacturer.lastUpdated = new Date();    //更新日期(不能为空)
                        manufacturer.fromWhere = "crawler_MozillaJob";    //数据来源
                        if (!manufacturer.save(flush: true)) {
                            manufacturer.errors.allErrors.each { println it }
                        }
                        def manufacturerLi = Manufacturer.findByName(changshang)
                        flawInstanceList.manufacturer = manufacturerLi;
                    } else {
                        flawInstanceList.manufacturer = manufacturerList;
                    }

                    flawInstanceList.detailedInfo = detailedInfoList;
                    if (!flawInstanceList.save(flush: true)) {
                        flawInstanceList.errors.allErrors.each { println it }
                    }
                    //外部引用入库
                    def flawList = Flaw.findByTitle(title);

                    for (int l = 0; l < maps.size(); l++) {
                        //外部引用
                        ReferenceInfo referenceInfo = new ReferenceInfo();
                        ReferenceType referenceType = new ReferenceType();
                        String ReferenceTypeName = maps.get(l).get("ReferenceTypeName");
                        String linkUrl = maps.get(l).get("linkUrl");
                        String referenceNumber = maps.get(l).get("referenceNumber");
                        def referenceTypeList = ReferenceType.findByName(ReferenceTypeName);
                        referenceInfo.referenceType = referenceTypeList;
                        referenceInfo.flaw = flawList;
                        referenceInfo.referenceNumber = referenceNumber;
                        referenceInfo.linkUrl = linkUrl;
                        referenceInfo.fromWhere = "crawler_MozillaJob";    //数据来源

                        if (!referenceInfo.save(flush: true)) {
                            referenceInfo.errors.allErrors.each { println it }
                            println "外部引用错误！"
                        }
                    }
                    webLog.number = webbakstr;
                    webLog.webnum = "Mozilla";
                    webLog.submitDate = new Date();
                    webLog.save(flush: true);
                }

            }
        }
    }


    def kbcerttest = {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        println "kb.certJob start！" + dateFormat.format(date);

        //—————————————爬取部分—————————————
        String spec = "https://www.kb.cert.org/vuls/";
        org.jsoup.nodes.Document doc = null;
        ;
        try {
            System.setProperty("https.protocols", "TLSv1.2,TLSv1.1,SSLv3");
            doc = Jsoup.connect(spec).userAgent(USERAGENT).timeout(TIMEOUT).get();
        } catch (IOException e1) {
            e1.printStackTrace();
        }
        Elements ets = doc.getElementsByClass("vulnerability-list")
        for (int i = 0; i < ets.size(); i++) {
            //防止某些字段获取不到导致其他字段获取不到
            String webbakstr;
            String titleEN;
            String url;
            org.jsoup.nodes.Element wrap;
            String detailsEN;
            String formalWayEN;
            String changshang;
            Date foundTime = null;
            Date submitTime = null;
            Date updateTime = null;
            String foundTimeStr;
            String submitTimeStr;
            String updateTimeStr;
            try {
                def aText = ets[i].select("a[href]").text();
                String[] split = aText.split(": ")
                //本网站编号
                webbakstr = split[0];
                //漏洞标题(翻译)
                titleEN = split[1];
                //获取连接href
                url = ets[i].select("a[href]").attr("href");
                String spec1 = "http://www.kb.cert.org" + url;
                org.jsoup.nodes.Document doc1 = null;
                try {
                    doc1 = Jsoup.connect(spec1).userAgent(USERAGENT).timeout(
                            TIMEOUT).get();
                } catch (IOException e) {
                    e.printStackTrace();
                    continue;
                }
                wrap = doc1.getElementById("content");
                //漏洞描述(翻译)
                detailsEN = wrap.getElementsByTag("td").get(0).text();
                //解决方案(翻译)
                formalWayEN = wrap.getElementsByTag("td").get(2).text();
                //厂商
                def divDocs = wrap.getElementsByClass("vinfo");
                changshang = divDocs[0].attr("name")
                //漏洞发现时间
                foundTimeStr = wrap.getElementsByClass("unstriped").get(0).getElementsByTag("td").get(3).text();
                //漏洞报送时间
                submitTimeStr = wrap.getElementsByClass("unstriped").get(0).getElementsByTag("td").get(5).text();
                //漏洞更新时间
                updateTimeStr = wrap.getElementsByClass("unstriped").get(0).getElementsByTag("td").get(7).text();
            } catch (Exception e) {
                e.printStackTrace()
                continue;
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");//日期类型转换

            try {
                foundTime = sdf.parse(foundTimeStr);
                submitTime = sdf.parse(submitTimeStr);
                updateTime = sdf.parse(updateTimeStr);
            } catch (Exception e) {
                e.printStackTrace()
                continue;
            }
            //外部引用CVE
            List<Map<String, String>> maps = new ArrayList<Map<String, String>>();
            def cveCount = wrap.getElementsByClass("unstriped").get(0).getElementsByTag("td").get(1).select("a[href]");
            //有多少CVE
            String cveNum = cveCount.text()
            if (cveNum != null && cveNum != "") {
                def referenceInfolist = ReferenceInfo.findByReferenceNumber(cveNum);
                if (referenceInfolist) {
                    continue;
                }
            }
            String cveLink = cveCount.attr("href");
            Map<String, String> mapCve = new HashMap<String, String>();
            mapCve.put("ReferenceTypeName", "CVE");
            mapCve.put("linkUrl", cveLink);
            mapCve.put("referenceNumber", cveNum);
            maps.add(mapCve);

            //其他引用
            Elements otherLink = wrap.getElementsByClass("vulreflink").select("a[href]");
            for (int j = 0; j < otherLink.size(); j++) {
                Map<String, String> map = new HashMap<String, String>();
                String urlLink = otherLink[j].attr("href");
                map.put("ReferenceTypeName", "其他");
                map.put("linkUrl", urlLink);
                map.put("referenceNumber", "");
                maps.add(map);
            }
            int length = maps.size();
            //—————————————翻译部分—————————————
            def title;
            def details;
            def formalWay;
            try {
                title = BaiduTranslate.getTransResult(titleEN, "en", "zh");        //翻译标题
                details = BaiduTranslate.getTransResult(detailsEN, "en", "zh");    //翻译描述
                formalWay = BaiduTranslate.getTransResult(formalWayEN, "en", "zh");        //翻译解决方法
            } catch (Exception e) {
                e.printStackTrace();
                System.out.println("翻译出错，job为KbcertJob，参考百度错误代码和说明。");
                continue;
            }
            //—————————————入库部分—————————————
            //查询CNVD工作组Tuser
            def cnvdUser = TUser.get(1);
            //漏洞表
            Flaw flawInstanceList = new Flaw();
            //漏洞详细信息
            DetailedInfo detailedInfoList = new DetailedInfo();
            //爬取网站记录表
            WebLog webLog = new WebLog();

            //厂商表
            Manufacturer manufacturer = new Manufacturer();
            //通过weblog中的记录验证本条数据是否已经存储
            def webLogList = WebLog.findByNumber(webbakstr)
            if (!webLogList) {
                //保存
                flawInstanceList.fromWhere = "crawler_KbcertJob";    //数据来源
                flawInstanceList.dateCreated = new Date();    //录入时间(不能为空)
                flawInstanceList.enable = 1; //1可用(不能为空)
                flawInstanceList.foundTime = foundTime;      //发现时间(不能为空)
                flawInstanceList.submitTime = submitTime; //漏洞报送时间 (不能为空)
                flawInstanceList.lastUpdated = updateTime;    ////更新日期(不能为空)
                flawInstanceList.status = 4;    //此状态表示将数据放入补录列表中(不能为空)
                flawInstanceList.title = title;    //漏洞标题(不能为空)
                flawInstanceList.user = cnvdUser;    //漏洞录入人
                flawInstanceList.serverityId = 17;    //漏洞威胁大小	ID 高18  中19 低20 未知17
                flawInstanceList.isAdditional = 1; //1为补录漏洞

                detailedInfoList.description = details;    //漏洞描述
                detailedInfoList.formalWay = formalWay;        //正式解决办法（建议）
                detailedInfoList.fromWhere = "crawler_KbcertJob";    //数据来源

                //厂商
                def manufacturerList = Manufacturer.findByName(changshang)
                if (!manufacturerList) {
                    manufacturer.name = changshang;    //厂商名称(不能为空)
                    manufacturer.dateCreated = new Date();    //创建日期(不能为空)
                    manufacturer.lastUpdated = new Date();    //更新日期(不能为空)
                    manufacturer.fromWhere = "crawler_KbcertJob";    //数据来源
                    if (!manufacturer.save(flush: true)) {
                        manufacturer.errors.allErrors.each { println it }
                    }
                    def manufacturerLi = Manufacturer.findByName(changshang)
                    flawInstanceList.manufacturer = manufacturerLi;
                } else {
                    flawInstanceList.manufacturer = manufacturerList;
                }

                flawInstanceList.detailedInfo = detailedInfoList;
                if (!flawInstanceList.save(flush: true)) {
                    flawInstanceList.errors.allErrors.each { println it }
                }
                //外部引用入库
                def flawList = Flaw.findByTitle(title);

                for (int l = 0; l < maps.size(); l++) {
                    //外部引用
                    ReferenceInfo referenceInfo = new ReferenceInfo();
                    ReferenceType referenceType = new ReferenceType();
                    String ReferenceTypeName = maps.get(l).get("ReferenceTypeName");
                    String linkUrl = maps.get(l).get("linkUrl");
                    String referenceNumber = maps.get(l).get("referenceNumber");
                    def referenceTypeList = ReferenceType.findByName(ReferenceTypeName);
                    referenceInfo.referenceType = referenceTypeList;
                    referenceInfo.flaw = flawList;
                    referenceInfo.referenceNumber = referenceNumber;
                    referenceInfo.linkUrl = linkUrl;
                    referenceInfo.fromWhere = "crawler_KbcertJob";    //数据来源
                    if (!referenceInfo.save(flush: true)) {
                        referenceInfo.errors.allErrors.each { println it }
                        println "外部引用错误！"
                    }
                }
                webLog.number = webbakstr;
                webLog.webnum = "KbCert";
                webLog.submitDate = new Date();
                webLog.save(flush: true);
            }
        }
    }
    def jvndbtest = {
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        println "jvndbJob start！" + dateFormat.format(date);

        //—————————————爬取部分—————————————
        String spec = "http://jvndb.jvn.jp/en/";
        Document doc = null;
        try {
            System.setProperty("https.protocols", "TLSv1.2,TLSv1.1,SSLv3");
            doc = Jsoup.connect(spec).userAgent(USERAGENT).timeout(TIMEOUT).get();
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        Elements wrap = doc.getElementsByClass("node-wrapper");
        Elements wrap1 = wrap.select("a[href]");
        String url = "";
        for (int i = 0; i < wrap1.size() - 1; i++) {
            url = wrap1[i].getAllElements().attr("href");
            String spec1 = "http://jvndb.jvn.jp" + url;
//			System.out.println(spec1);

            Document doc1 = null;
            try {
                System.setProperty("https.protocols", "TLSv1.2,TLSv1.1,SSLv3");
                doc1 = Jsoup.connect(spec1).userAgent(USERAGENT).timeout(
                        TIMEOUT).get();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
                continue;
            }
            //
            org.jsoup.nodes.Element wrap2 = doc1.getElementsByClass("contents").first();
            Elements warp3 = wrap2.getElementsByTag("table");


            //本网站编号
            String webbakstr = warp3.get(0).getElementsByTag("td").get(1).text();
            //漏洞标题(需翻译)
            String titleEN = warp3.get(0).getElementsByTag("td").get(2).text();
            //漏洞描述(需翻译)
            String detailsEN = warp3.get(0).getElementsByTag("td").get(4).text();
            //危害等级
            String threadStrEN = warp3.get(0).getElementsByTag("td").get(6).getElementsByTag("b").text();
            int thread;
            //High Medium Low	ID 高18  中19 低20 未知17
            if (threadStrEN.contains("High")) {
                thread = 18;
            } else if (threadStrEN.contains("Medium")) {
                thread = 19;
            } else if (threadStrEN.contains("Low")) {
                thread = 20;
            } else {
                thread = 17;
            }
            //解决方案(需翻译)
            String formalWayEN = warp3.get(0).getElementsByTag("td").get(15).text();
            //外部引用CVE
            String  cveNum = "";
            String cveLink ="";
            def  cveNumElement = warp3.get(0).getElementsByTag("ol").get(1).getElementsByTag("li")
            if (cveNumElement.size()>0){
                cveNum = cveNumElement.get(0).text();    //CVE编号
                if (cveNum != "" && cveNum != null) {
                    //判断CVE是否存在，存在则不录入
                    def referenceInfolist = ReferenceInfo.findByReferenceNumber(cveNum);
                    if (referenceInfolist) {
                        continue;
                    }
                }

                cveLink = warp3.get(0).getElementsByTag("ol").get(1).getElementsByTag("li").get(0).getAllElements().attr("href");
            }


            //漏洞发现时间
            String foundTimeStr = warp3.get(1).getElementsByTag("td").get(2).text();
            //漏洞报送时间
            String submitTimeStr = warp3.get(1).getElementsByTag("td").get(4).text();
            //漏洞更新时间
            String updateTimeStr = warp3.get(1).getElementsByTag("td").get(6).text();

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
            Date foundTime = null;
            Date submitTime = null;
            Date updateTime = null;
            try {
                foundTime = sdf.parse(foundTimeStr);
                submitTime = sdf.parse(submitTimeStr);
                updateTime = sdf.parse(updateTimeStr);
            } catch (Exception e) {
                e.printStackTrace()
            }
            //—————————————翻译部分—————————————
            def title;
            def details;
            def formalWay;
            try {
                title = BaiduTranslate.getTransResult(titleEN, "en", "zh");        //翻译标题
                details = BaiduTranslate.getTransResult(detailsEN, "en", "zh");    //翻译描述
                formalWay = BaiduTranslate.getTransResult(formalWayEN, "en", "zh");        //翻译解决方法
            } catch (Exception e) {
                e.printStackTrace();
                System.out.println("翻译出错，job为JvndbJob，参考百度错误代码和说明。");
            }
            //—————————————入库部分—————————————
            //查询CNVD工作组Tuser
            def cnvdUser = TUser.get(1);
            //漏洞表
            Flaw flawInstanceList = new Flaw();
            //漏洞详细信息
            DetailedInfo detailedInfoList = new DetailedInfo();
            //爬取网站记录表
            WebLog webLog = new WebLog();
            //外部引用
            ReferenceInfo referenceInfo = new ReferenceInfo();

            //通过weblog中的记录验证本条数据是否已经存储
            def webLogList = WebLog.findByNumber(webbakstr)
            if (!webLogList) {
                //保存
                flawInstanceList.fromWhere = "crawler_JvndbJob";    //数据来源
                flawInstanceList.dateCreated = new Date();    //录入时间(不能为空)
                flawInstanceList.enable = 1; //1可用(不能为空)
                flawInstanceList.foundTime = foundTime;      //发现时间(不能为空)
                flawInstanceList.submitTime = submitTime; //漏洞报送时间 (不能为空)
                flawInstanceList.lastUpdated = updateTime;    ////更新日期(不能为空)
                flawInstanceList.status = 4;    //此状态表示将数据放入补录列表中(不能为空)
                flawInstanceList.title = title;    //漏洞标题(不能为空)
                flawInstanceList.user = cnvdUser;    //漏洞录入人
                flawInstanceList.serverityId = thread;    //漏洞威胁大小	ID 高18  中19 低20 未知17
                flawInstanceList.isAdditional = 1; //1为补录漏洞

                detailedInfoList.description = details;    //漏洞描述
                detailedInfoList.formalWay = formalWay;        //正式解决办法（建议）
                detailedInfoList.fromWhere = "crawler_JvndbJob";    //数据来源
                flawInstanceList.detailedInfo = detailedInfoList;
                if (!flawInstanceList.save(flush: true)) {
                    flawInstanceList.errors.allErrors.each { println it }
                }else{
                    //外部引用入库
                    if (cveNum&&cveLink){
                        def referenceTypeList = ReferenceType.findByName("CVE");
                        referenceInfo.referenceType = referenceTypeList;
                        referenceInfo.flaw = flawInstanceList;
                        referenceInfo.referenceNumber = cveNum;
                        referenceInfo.linkUrl = cveLink;
                        referenceInfo.fromWhere = "crawler_JvndbJob";    //数据来源

                        if (!referenceInfo.save(flush: true)) {
                            referenceInfo.errors.allErrors.each { println it }
                            println "外部引用！"
                        }
                    }
                }
                webLog.number = webbakstr;
                webLog.webnum = "jvndb";
                webLog.submitDate = new Date();
                webLog.save(flush: true);
            }

        }
    }


    def test = {
        try {
            java.text.DecimalFormat df = new java.text.DecimalFormat("#.00");
            def tusers = TUser.findAllByEnableAndStatus(1, 100304);
            for (TUser tuser : tusers) {
                //用户总积分
                Float sumTtotal = 0;
//				String flawHql = "from Flaw f where f.user = ? and f.points is not null";
//				def flawList = Flaw.executeQuery(flawHql,[tuser]);
//				for(Flaw flawInstance:flawList){
//					println "漏洞表中用户id为"+tuser.id+",积分数量不为空的数量为"+flawList.size()
//					sumTtotal += flawInstance?.points?.total
//
//				}
                String sql = "SELECT TRUNCATE(sum(points.total),2) AS sum FROM flaw,tuser,points WHERE tuser.id = ? and  flaw.user_id = tuser.id AND flaw.points_id = points.id AND tuser.id <> 1 AND flaw. STATUS = 9 AND flaw.ENABLE = 1 AND flaw.parent_flaw_id IS NULL GROUP BY flaw.user_id  "
                def rankLists = SQLUtil.getResult(sql, [tuser.id]);
                if (rankLists) {
                    sumTtotal = rankLists[0].sum
                }
//				println sumTtotal+"   ---"
//				println df.format(sumTtotal)+" ==="
                double sumto = Double.parseDouble(df.format(sumTtotal));
                String email = tuser.email;
                String name = tuser.nickName;
                String password = tuser.password;//密码
                if (!name) {
                    name = ""
                }
                int userType = tuser.userType;
                def pushTuserLog = PushTuserLog.findByTuserid(tuser.id);
                if (pushTuserLog) {
                    if (password.equals(pushTuserLog.password) && email.equals(pushTuserLog.email) && name.equals(pushTuserLog.name) && sumto == pushTuserLog.sumTtotal && userType == pushTuserLog.userType) {
                        continue;
                    } else {
                        //更新log表
                        pushTuserLog.sumTtotal = sumto;
                        pushTuserLog.email = email;
                        pushTuserLog.name = name;
                        pushTuserLog.userType = userType;
                        pushTuserLog.password = password;
                        pushTuserLog.save();
                        String aesStr = email + "#@#" + sumto + "#@#" + name + "#@#" + userType + "#@#" + tuser.id + "#@#" + password
//						println aesStr
                        String aes = AESCrypt.Encrypt(aesStr);
//						println aes
                        String url = "http://**************:8881/shoppingAdmin/pushTuser/updateTuser"
                        int str = HttpClientUtil.send(aes, url);
                    }
                } else {
                    PushTuserLog pushTuserLogInstance = new PushTuserLog();
                    pushTuserLogInstance.tuserid = tuser.id;
                    pushTuserLogInstance.email = tuser.email;
                    pushTuserLogInstance.name = tuser.nickName;
                    pushTuserLogInstance.userType = tuser.userType;
                    pushTuserLogInstance.sumTtotal = sumto;
                    pushTuserLogInstance.password = password;
                    pushTuserLogInstance.save()
                    String aesStr = email + "#@#" + sumto + "#@#" + name + "#@#" + userType + "#@#" + tuser.id + "#@#" + password
//					println aesStr
                    String aes = AESCrypt.Encrypt(aesStr);
//					println aes
                    String url = "http://**************:8881/shoppingAdmin/pushTuser/sendTuser"
                    int str = HttpClientUtil.send(aes, url);
                }

            }
        } finally {
            //			isOver = true
        }


    }

    def long getFileSize(File file) {
        long s = 0;
        if (file.exists()) {
            FileInputStream fis = null;
            fis = new FileInputStream(file);
            s = fis.available();
        }
        return s;
    }
    def index = {}
    def test1 = {
        def apiurl = "http://localhost:8777/createVitualMachine?imageUuid=be5284095b2e4c8988fa46cb997c2974"
        def resstr = "";
        URL url = new URL(apiurl);
        java.net.HttpURLConnection httpurlconnection = (java.net.HttpURLConnection) url.openConnection();
        httpurlconnection.setDoOutput(true);
        httpurlconnection.setRequestMethod("POST");
        java.io.BufferedReader inb;
        try {
            inb = new java.io.BufferedReader(new java.io.InputStreamReader(httpurlconnection.getInputStream()));
            println "获取任务连接成功============="
        } catch (Exception e) {
            e.printStackTrace();
            println "获取任务详细失败"
        }
        String line;
        while ((line = inb.readLine()) != null) {
            resstr += line;
        }
        inb.close();
        println resstr
    }

    def test2 = {
        println "CreateTxtJob执行" + new Date()
        SimpleDateFormat sd = new SimpleDateFormat("yyyy")
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        //昨天天数
//		Calendar calendar = Calendar.getInstance();
//		calendar.setTime(new Date());
//		calendar.add(Calendar.DAY_OF_MONTH, -1);
//		Date date = sdf.parse(sdf.format(calendar.getTime()));
//		println sdf.format(calendar.getTime())
        String ss = sd.format(new Date())
        Date date = sdf.parse("2017-10-18");
        Date nowDate = sdf.parse("2017-11-09");
//		println sd.format(new Date())

        //当前天数
//		Date nowDate = sdf.parse(sdf.format(new Date()));
//		println sdf.format(new Date())
        //漏洞表查询
        def hql = "from Flaw t where t.status = 9 and t.enable=1 and t.parentFlaw is null and t.storageTime >= :storageTime and t.storageTime < :nowDate";
//		def hql = "from Flaw t where t.status = 9 and t.enable=1 and t.parentFlaw is null and  t.storageTime < :nowDate";
        def hqlPara = new HashMap();
        hqlPara.put("storageTime", date)
        hqlPara.put("nowDate", nowDate)
        hql += " order by t.storageTime desc"
        def flawList = Flaw.executeQuery(hql, hqlPara)

        //定义txt内容
        String flawTxt = "";
        String productTxt = "";
        String referenceTxt = "";
        String ipTxt = "";
        String domainNameTxt = "";

        for (Flaw flawInstance : flawList) {
            String flawId = flawInstance.id;    //漏洞id
            String number = flawInstance.number;    //漏洞编号
            String title = flawInstance.title;    //漏洞编号
            String isFirst = null;    //是否首次公开
            if (flawInstance.isFirst) {
                if (flawInstance.isFirst == 1) {
                    isFirst = "是"
                } else {
                    isFirst = "否"
                }
            } else {
                isFirst = "否"
            }
            String isZero = null;    //是否零日
            if (flawInstance.isZero) {
                if (flawInstance.isZero == 1) {
                    isZero = "是"
                } else {
                    isZero = "否"
                }
            } else {
                isZero = "否"
            }
            String foundTime = null;    //发现时间
            if (flawInstance.foundTime) {
                foundTime = sdf1.format(flawInstance.foundTime)
            } else {
                foundTime = sdf1.format(flawInstance.dateCreated)
            }
            String dateCreated = sdf1.format(flawInstance.dateCreated) //漏洞创建时间
            String causeId = DictionaryInfo.get(flawInstance?.causeId)?.name //漏洞产生原因
            String threadId = DictionaryInfo.get(flawInstance?.threadId)?.name //漏洞引发的威胁
            String serverityId = DictionaryInfo.get(flawInstance?.serverityId)?.name //漏洞严重程度
            String positionId = DictionaryInfo.get(flawInstance?.positionId)?.name //漏洞利用的攻击位置
            String softStyleId = DictionaryInfo.get(flawInstance?.softStyleId)?.name //漏洞影响对象类型
            if (softStyleId != null) {
                softStyleId = softStyleId.replaceAll("\r|\n|\t|\r\n", "");
            }
            String discovererName = flawInstance?.discovererName //发现者姓名
            String referenceLink = flawInstance?.referenceLink //漏洞参考链接
            if (referenceLink) {
                referenceLink = referenceLink.replaceAll("\r|\n|\t|\r\n", " ");
            }
            String isEvent = flawInstance?.isEvent == 1 ? '是' : '否' //是否为事件型漏洞
            String description = flawInstance?.detailedInfo?.description
            if (description != null) {
                description = description.replaceAll("\r|\n|\t|\r\n", ""); //漏洞描述
            }
            String tempWay = flawInstance?.detailedInfo?.tempWay
            if (tempWay != null) {
                tempWay = tempWay.replaceAll("\r|\n|\t|\r\n", ""); //临时解决办法
            }
            String formalWay = flawInstance?.detailedInfo?.formalWay
            if (formalWay != null) {
                formalWay = formalWay.replaceAll("\r|\n|\t|\r\n", ""); //正式解决办法
            }
            //storageTime 归档时间
            String storageTime = sdf1.format(flawInstance.storageTime);

            //补丁信息
            PatchInfo patchInstance = PatchInfo.findByFlaw(flawInstance);
            //漏洞信息
            String patchName = patchInstance?.patchName //补丁名称
            String patchUrl = patchInstance?.patchUrl //补丁链接
            if (patchUrl) {
                patchUrl = patchUrl.replaceAll("\r|\n|\t|\r\n", " ");
            }
            String function = patchInstance?.function //补丁验证原理
            String patchDescription = patchInstance?.patchDescription
            if (patchDescription != null) {
                patchDescription = patchDescription.replaceAll("\r|\n|\t|\r\n", ""); //补丁描述
            }

            //拼接漏洞信息
            String flawStr = flawId + "&&&&&" + number + "&&&&&" + isFirst + "&&&&&" + isZero + "&&&&&" + foundTime + "&&&&&" + dateCreated + "&&&&&" + causeId + "&&&&&" + threadId + "&&&&&" + serverityId + "&&&&&" + positionId + "&&&&&" + softStyleId + "&&&&&" + discovererName + "&&&&&" + referenceLink + "&&&&&" + isEvent + "&&&&&" + description + "&&&&&" + tempWay + "&&&&&" + formalWay + "&&&&&" + patchName + "&&&&&" + patchUrl + "&&&&&" + function + "&&&&&" + patchDescription + "&&&&&" + storageTime + "&&&&&" + title;
            flawTxt += flawStr.replaceAll("\r|\n|\t|\r\n", "") + "\r\n";
            //资产信息
            List<FlawProduct> fpList = FlawProduct.findAllByFlaw(flawInstance);
            for (FlawProduct fp : fpList) {
                ProductInfo productInfoInstance = fp.product;
                String productId = productInfoInstance.id; //资产id
                String manufacturer = productInfoInstance?.manufacturer?.name //厂商名称
                String productCategory = productInfoInstance?.productCategory?.name  //产品名称
                String edition = productInfoInstance?.edition //产品版本
                //拼接资产信息
                String productStr = productId + "&&&&&" + flawId + "&&&&&" + manufacturer + "&&&&&" + productCategory + "&&&&&" + edition;
                productTxt += productStr.replaceAll("\r|\n|\t|\r\n", "") + "\r\n";
            }

            //外部引用
            def referenceList = ReferenceInfo.findAllByFlaw(flawInstance)
            for (ReferenceInfo referenceInstance : referenceList) {
                String referenceId = referenceInstance.id  //引用id
                String referenceType = referenceInstance?.referenceType?.name //引用类型
                String referenceNumber = referenceInstance?.referenceNumber  //引用编号
                String linkName = referenceInstance?.linkName //引用名称
                String linkUrl = referenceInstance?.linkUrl //引用链接
                //拼接引用信息
                String referenceStr = referenceId + "&&&&&" + flawId + "&&&&&" + referenceType + "&&&&&" + referenceNumber + "&&&&&" + linkName + "&&&&&" + linkUrl;
                referenceTxt += referenceStr.replaceAll("\r|\n|\t|\r\n", "") + "\r\n";
            }

            //与漏洞相关的域名   ip
            def flawUrlList = FlawUrl.findAllByFlaw(flawInstance)
//			String domainNameStr = ""
//			String ipStr = ""
            for (FlawUrl fu : flawUrlList) {
                String text = fu.url
                println text
                //提取域名
                String regex0 = ",?(\\www.+\\.(com(.cn)?|net|cn|top|xyz|cx|red|org|gov|edu|mil|biz|cn|name|info|mobi|arpa|pro|museum))";
                Matcher m = Pattern.compile(regex0, Pattern.CASE_INSENSITIVE).matcher(text);
                while (m.find()) {
//					domainNameStr+=m.group().substring(4)+"&&&&&"
                    domainNameTxt += (flawId + "&&&&&" + m.group().substring(4)).replaceAll("\r|\n|\t|\r\n", "") + "\r\n";
                    System.out.println(m.group().substring(4));
                }
                //提取ip
                String regex1 = "((?:(?:25[0-5]|2[0-4]\\d|[01]?\\d?\\d)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d?\\d))";
                Matcher m2 = Pattern.compile(regex1, Pattern.CASE_INSENSITIVE).matcher(text);
                while (m2.find()) {
//					ipStr+=m2.group(1)+"&&&&&"
                    ipTxt += (flawId + "&&&&&" + m2.group(1)).replaceAll("\r|\n|\t|\r\n", "") + "\r\n";
                    System.out.println(m2.group(1));
                }
            }
            println "==================="
        }
        String dateStr = sdf.format(new Date())
        //生成文件
//		String flawPath = "/cnvd/TXT/flaw";
        String flawPath = "${config.filePath.TxtFlawPath}";
        String flawName = "flaw_" + dateStr + ".txt";
        FilesUtil.writeTxt(flawPath, flawName, flawTxt)
//		String productPath = "/cnvd/TXT/product";
        String productPath = "${config.filePath.TxtProductPath}";
        String productName = "product_" + dateStr + ".txt";
        FilesUtil.writeTxt(productPath, productName, productTxt)
//		String referencePath = "/cnvd/TXT/reference";
        String referencePath = "${config.filePath.TxtReferencePath}";
        String referenceName = "reference_" + dateStr + ".txt";
        FilesUtil.writeTxt(referencePath, referenceName, referenceTxt)
//		String domainNamePath = "/cnvd/TXT/domainName";
        String domainNamePath = "${config.filePath.TxtDomainNamePath}";
        String domainNameName = "domainName_" + dateStr + ".txt";
        FilesUtil.writeTxt(domainNamePath, domainNameName, domainNameTxt)
//		String ipPath = "/cnvd/TXT/ip";
        String ipPath = "${config.filePath.TxtIpPath}";
        String ipName = "ip_" + dateStr + ".txt";
        FilesUtil.writeTxt(ipPath, ipName, ipTxt)
        println "生成txt结束"
    }

    /**
     * 漏洞利用代码转换
     */
    def test3 = {
        def list = ExploitTemp.executeQuery("from ExploitTemp f where f.flag is null OR f.flag = 0")
        for (ExploitTemp ep : list) {
            if (ep.cnvd && !"".equals(ep.cnvd)) {
                Flaw fl = Flaw.findByNumberAndEnable(ep.cnvd, 1)
                if (fl) {
                    ExploitTrue er = new ExploitTrue()
                    er.number = ep?.number
                    er.vendor = ep?.vendor
                    er.product = ep?.product
                    er.proVersion = ep?.proVersion
                    er.vulnerabillityName = fl?.title
                    if (fl?.detailedInfo?.description) {
                        er.vulnerabillityDescribe = fl?.detailedInfo?.description
                    } else {
                        er.vulnerabillityDescribe = ep?.vulnerabillityDescribe
                    }
                    er.cve = ep?.cve
                    er.bid = ep?.bid
                    er.cnvd = ep?.cnvd
                    er.expName = ep?.expName
                    //附件
                    er.expFile = ep?.expFile
                    er.platform = ep?.platform
                    er.type = ep?.type
                    er.dockerType = ep?.dockerType
                    er.expEnvironment = ep?.expEnvironment
                    er.port = ep?.port
                    er.refer = ep?.refer
                    er.status = ep?.status
                    er.save(flush: true)
                } else {
                    ExploitTrue er = new ExploitTrue()
                    er.number = ep?.number
                    er.vendor = ep?.vendor
                    er.product = ep?.product
                    er.proVersion = ep?.proVersion
                    er.vulnerabillityName = ep?.vulnerabillityName
                    er.vulnerabillityDescribe = ep?.vulnerabillityDescribe
                    er.cve = ep?.cve
                    er.bid = ep?.bid
                    er.cnvd = ep?.cnvd
                    er.expName = ep?.expName
                    //附件
                    er.expFile = ep?.expFile
                    er.platform = ep?.platform
                    er.type = ep?.type
                    er.dockerType = ep?.dockerType
                    er.expEnvironment = ep?.expEnvironment
                    er.port = ep?.port
                    er.refer = ep?.refer
                    er.status = ep?.status
                    er.save(flush: true)
                }
            } else {
                ExploitTrue er = new ExploitTrue()
                er.number = ep?.number
                er.vendor = ep?.vendor
                er.product = ep?.product
                er.proVersion = ep?.proVersion
                er.vulnerabillityName = ep?.vulnerabillityName
                er.vulnerabillityDescribe = ep?.vulnerabillityDescribe
                er.cve = ep?.cve
                er.bid = ep?.bid
                er.cnvd = ep?.cnvd
                er.expName = ep?.expName
                //附件
                er.expFile = ep?.expFile
                er.platform = ep?.platform
                er.type = ep?.type
                er.dockerType = ep?.dockerType
                er.expEnvironment = ep?.expEnvironment
                er.port = ep?.port
                er.refer = ep?.refer
                er.status = ep?.status
                er.save(flush: true)
            }
            ep.flag = 1
            ep.save(flush: true)
        }
        println " finish === "
    }

    /**
     * 资产信息导出
     */
    def test4 = {
        Industry industryInstance = Industry.get(3)
        def list = IndustryFlaw.executeQuery("from IndustryFlaw f where f.industry = ?", [industryInstance])

        //excel
        WorkbookSettings workbookSettings = new WorkbookSettings()
        workbookSettings.locale = Locale.default

        def file = File.createTempFile('myExcelDocument', '.xls')
        file.deleteOnExit()

        WritableWorkbook workbook = Workbook.createWorkbook(file, workbookSettings)

        WritableFont font = new WritableFont(WritableFont.ARIAL, 12)
        WritableCellFormat format = new WritableCellFormat(font)

        def row = 0
        WritableSheet sheet = workbook.createSheet('工控系统行业漏洞', 0)
        sheet.addCell(new Label(0, row, "漏洞编号", format))
        sheet.addCell(new Label(1, row, "漏洞标题", format))
        sheet.addCell(new Label(2, row, "漏洞描述", format))
        sheet.addCell(new Label(3, row, "参考链接", format))
        sheet.addCell(new Label(4, row, "类别（通过关键字关联/通过资产关联）", format))
        sheet.addCell(new Label(5, row, "关联的关键词/资产", format))
        row++
        for (IndustryFlaw idf : list) {
            String number = idf.flaw.number  //漏洞编号
            String title = idf.flaw.title  //漏洞标题
            String description = idf.flaw.detailedInfo.description ? idf.flaw.detailedInfo.description : ""
            String link = idf.flaw.referenceLink ? idf.flaw.referenceLink : ""
            String type = ""
            if (idf.type == 0) {
                type = "资产匹配"
            } else {
                type = "关键字匹配"
            }
            String str = ""
            if (idf.type == 0) {
                //厂商
                def flawIndustryProductList = FlawIndustryProduct.executeQuery(" from FlawIndustryProduct fip where flaw=? and industry=?", [idf.flaw, industryInstance])
                for (FlawIndustryProduct flawIndustryProduct : flawIndustryProductList) {
                    str += flawIndustryProduct?.corporationProduct?.manufacturer?.name + "  " + flawIndustryProduct?.corporationProduct?.productCategory ? flawIndustryProduct.corporationProduct.productCategory.name : '' + "  " + flawIndustryProduct?.corporationProduct ? flawIndustryProduct.corporationProduct.edition : '' + "#####"
                }
            } else if (idf.type == 1) {
                //关键字
                def keywordList = FlawIndustryKeyword.executeQuery("select fik.keyword from FlawIndustryKeyword fik where fik.flaw=? and fik.industry=?", [idf.flaw, industryInstance])
                for (KeyWord kw : keywordList) {
                    str += kw.word + "#####"
                }
            }
            sheet.addCell(new Label(0, row, number, format))
            sheet.addCell(new Label(1, row, title, format))
            sheet.addCell(new Label(2, row, description, format))
            sheet.addCell(new Label(3, row, link, format))
            sheet.addCell(new Label(4, row, type, format))
            sheet.addCell(new Label(5, row, str, format))
            row++
        }
        workbook.write()
        workbook.close()
//		return file
        response.setHeader('Content-disposition', 'attachment;filename=myflaw.xls')
        response.setHeader('Content-length', "${file.size()}")

        OutputStream out = new BufferedOutputStream(response.outputStream)

        try {
            out.write(file.bytes)

        } finally {
            out.close()
            return false
        }
    }

    //提取漏洞url
    def test5 = {
        String sql = "SELECT t.id FROM flaw t WHERE t.`enable` = 1 ORDER BY t.date_created "
        def idList = SQLUtil.getList(sql)
        for (String fid : idList) {
            try {
                Flaw flawInstance = Flaw.get(fid.id)
                //先提取漏洞描述中的url
                if (flawInstance.detailedInfo.description) {
                    String text = flawInstance.detailedInfo.description
                    //提取url
                    // url正则
                    String regex2 = "((https|http|ftp|rtsp|mms)?:\\/\\/([\\w-]+\\.)+[\\w-]+([\\w-./:?%&*=]*))";
                    Matcher m7 = Pattern.compile(regex2, Pattern.CASE_INSENSITIVE).matcher(text);
                    while (m7.find()) {
                        FlawUrl fu = FlawUrl.findByUrlAndFlaw(m7.group(), flawInstance)
                        if (!fu) {
                            FlawUrl flawUrl = new FlawUrl()
                            flawUrl.flaw = flawInstance
                            flawUrl.url = m7.group()
                            if (!flawUrl.save(flush: true)) {
                                flawUrl.errors.allErrors.each { println it }
                            }
                            //保存漏洞url到redis
                            flawService.setFlawUrl(flawInstance.getId(),m7.group())
                        }
                    }
                }
                //提取漏洞附件中的url
                if (flawInstance.attachment) {
                    String text = ReadWord.readWord(flawInstance.attachment.path);
                    String regex2 = "((https|http|ftp|rtsp|mms)?:\\/\\/([\\w-]+\\.)+[\\w-]+([\\w-./:?%&*=]*))";
                    Matcher m7 = Pattern.compile(regex2, Pattern.CASE_INSENSITIVE).matcher(text);
                    while (m7.find()) {
                        FlawUrl fu = FlawUrl.findByUrlAndFlaw(m7.group(), flawInstance)
                        if (!fu) {
                            FlawUrl flawUrl = new FlawUrl()
                            flawUrl.flaw = flawInstance
                            flawUrl.url = m7.group()
                            if (!flawUrl.save(flush: true)) {
                                flawUrl.errors.allErrors.each { println it }
                            }
                            //保存漏洞url到redis
                            flawService.setFlawUrl(flawInstance.getId(),m7.group())
                        }
                    }
                }
            } catch (Exception e) {
                println "id为" + fid + "的漏洞，提取出现问题"
                continue;
                //e.printStackTrace()
            }

        }
        println 222
    }

    /**
     * 以flaw_url表生成ip和域名
     */
    def test6 = {

        println "CreateTxtJob执行" + new Date()
//		def sql = "SELECT id FROM flaw_url LIMIT 1000";
//		SELECT id FROM flaw_url t WHERE t.date_created <= '2017-09-10';
//		SELECT * FROM flaw_url t WHERE t.date_created <= '2017-09-17' AND t.date_created > '2017-09-10';
//		SELECT * FROM flaw_url t WHERE t.date_created > '2017-09-17';
        def sql = "SELECT id FROM flaw_url t WHERE t.date_created > '2017-09-17'";
        def idList = SQLUtil.getList(sql)
        //定义txt内容
        String ipTxt = "";
        String domainNameTxt = "";

        for (String ids : idList) {
            FlawUrl flawUrl = FlawUrl.get(ids.id)
            //与漏洞相关的域名   ip
            String text = flawUrl.url
            println text
            //提取域名
            String regex0 = ",?(\\www.+\\.(com(.cn)?|net|cn|top|xyz|cx|red|org|gov|edu|mil|biz|cn|name|info|mobi|arpa|pro|museum))";
            Matcher m = Pattern.compile(regex0, Pattern.CASE_INSENSITIVE).matcher(text);
            while (m.find()) {
                domainNameTxt += (flawUrl.flaw.id + "&&&&&" + m.group().substring(4)).replaceAll("\r|\n|\t|\r\n", "") + "\r\n";
                System.out.println(m.group().substring(4));
            }
            //提取ip
            String regex1 = "((?:(?:25[0-5]|2[0-4]\\d|[01]?\\d?\\d)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d?\\d))";
            Matcher m2 = Pattern.compile(regex1, Pattern.CASE_INSENSITIVE).matcher(text);
            while (m2.find()) {
                ipTxt += (flawUrl.flaw.id + "&&&&&" + m2.group(1)).replaceAll("\r|\n|\t|\r\n", "") + "\r\n";
                System.out.println(m2.group(1));
            }
            println "==================="
        }
        String dateStr = formatter.format(new Date())
        //生成文件
        String domainNamePath = "/cnvd/TXT/domainName";
//		String domainNamePath = "${config.filePath.TxtDomainNamePath}";
        String domainNameName = "domainName_第三部分.txt";
        FilesUtil.writeTxt(domainNamePath, domainNameName, domainNameTxt)
        String ipPath = "/cnvd/TXT/ip";
//		String ipPath = "${config.filePath.TxtIpPath}";
        String ipName = "ip_第三部分.txt";
        FilesUtil.writeTxt(ipPath, ipName, ipTxt)
        println "生成txt结束"

    }

    /**
     * SQL网站去重，搜索标题中含有SQL注入、上报时间为2016-09-18以后的漏洞。通过url去重
     * @param args
     */

    def test7 = {
        println "search start"
        POIExcelUtil excelUtil = new POIExcelUtil("E:/cnvd/114.xlsx");
        int sheetNum = excelUtil.sheetNum(POIExcelUtil.wb);
        //从第三行第二列开始读1,2
        List<ArrayList<String>> dataList = excelUtil.read(POIExcelUtil.wb, 0, 1);
        for (ArrayList<String> innerList : dataList) {
            ok:
//			println innerList[2]
            String sql = "SELECT t.id FROM flaw t WHERE t.`enable` = 1 AND t.title LIKE '%SQL注入%' AND t.date_created >= '2016-09-18' AND t.`status` !=-2 AND t.`status`!= -1"
            def idList = SQLUtil.getList(sql)
            for (String fid : idList) {
                try {

                    Flaw flawInstance = Flaw.get(fid.id)
                    if (flawInstance.detailedInfo) {
                        if (flawInstance.detailedInfo.description.indexOf(innerList[2]) != -1) {
                            System.out.println(innerList[0]);
                            break ok;
                        }
                    }

                } catch (Exception e) {
                    println fid.id + "__err"
                    continue;
                }
            }

        }

        println "search stop ====="
    }

    /**
     * 监控19大网站数据
     * @param args
     */
    def test8 = {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar c = Calendar.getInstance();
        Date now = c.getTime()
        println now
        System.out.println(sdf.format(c.getTime()));
        c.add(Calendar.HOUR_OF_DAY, -1);//1小时前
        Date befor = c.getTime()
        println befor
        System.out.println(sdf.format(c.getTime()));

        def flawList = Flaw.executeQuery("from Flaw f where f.dateCreated >= ? and f.dateCreated < ?", [befor, now])
        String sql = "SELECT domain_name FROM check_nineteen"
        def domainList = SQLUtil.getList(sql)
        for (Flaw flawInstance : flawList) {
            String domainStr = ""
            for (String domain : domainList) {
                if (domain.domain_name && !"".equals(domain.domain_name.trim())) {
                    if (flawInstance.detailedInfo) {
                        if (flawInstance.detailedInfo.description.indexOf(domain.domain_name) != -1) {
                            domainStr += domain.domain_name + ","
                        }
                    }
                }
            }
            if (!"".equals(domainStr)) {
                domainStr = domainStr.substring(0, domainStr.length() - 1)
                //发邮件通知
                SendMail sendMail = SendMail.getInstance();
                String title = "19大域名数据监测";
                String contents = "域名【" + domainStr + "】在漏洞【" + flawInstance.title + "】中出现,该漏洞的编号为:" + flawInstance.tempNumber;
                def to = "<EMAIL>;<EMAIL>;<EMAIL>".split(";");
//				def to = "<EMAIL>;<EMAIL>".split(";");

                String[] cs = null;//抄送
                String[] ms = null;//密送

                String subject = title;
                String content = contents;
                String fileName = null; // 附件地址
                sendMail.send(to, cs, ms, subject, content, fileName);

            }
        }

        println 111

    }

    /**
     * 通过客户给的域名匹配到漏洞，在输出漏洞的指定信息
     */
    def test9 = {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
        //excel
        WorkbookSettings workbookSettings = new WorkbookSettings()
        workbookSettings.locale = Locale.default
        def file = File.createTempFile('myExcelDocument', '.xls')
        file.deleteOnExit()
        WritableWorkbook workbook = Workbook.createWorkbook(file, workbookSettings)
        WritableFont font = new WritableFont(WritableFont.ARIAL, 12)
        WritableCellFormat format = new WritableCellFormat(font)
        def row = 0
        WritableSheet sheet = workbook.createSheet('域名匹配信息', 0)
        sheet.addCell(new Label(0, row, "时间", format))
        sheet.addCell(new Label(1, row, "漏洞标题", format))
        sheet.addCell(new Label(2, row, "CVSS", format))
        sheet.addCell(new Label(3, row, "域名", format))
        row++
        //读txt，获取漏洞信息
        BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream("F:\\20171121.txt"), "UTF-8"));
        String s = null;
        int i = 0;
        ok:
        while ((s = br.readLine()) != null) {
            System.out.println(s);
            //匹配FlawUrl
//			def fuList = FlawUrl.findAllByUrl(s.trim())
            def fuList = FlawUrl.executeQuery("from FlawUrl f where f.url like '%" + s.trim() + "%'")
            for (FlawUrl fu : fuList) {
                if (fu.flaw.status == 2 || fu.flaw.status == 3 || fu.flaw.status == 9) {
                    String date = sdf.format(fu.flaw.dateCreated)
                    String title = fu.flaw.title
                    Float score = 0;
                    try {
                        if (fu.flaw.basemetric) {
                            if (fu.flaw.basemetric.score) {
                                score = fu.flaw.basemetric.score
                            }
                        }
                    } catch (Exception e) {
                        score = 0
                    }
                    //每行写入
                    sheet.addCell(new Label(0, row, date, format))
                    sheet.addCell(new Label(1, row, title, format))
                    sheet.addCell(new Label(2, row, score + "", format))
                    sheet.addCell(new Label(3, row, s.trim(), format))
                    row++
                    i++
                    if (i >= 65536) {
                        println s.trim() + "循环url==============================================="
                        break ok;
                    }
                }
            }
        }
        br.close();
        workbook.write()
        workbook.close()
        response.setHeader('Content-disposition', 'attachment;filename=myflaw1.xls')
        response.setHeader('Content-length', "${file.size()}")
        OutputStream out = new BufferedOutputStream(response.outputStream)
        try {
            out.write(file.bytes)
        } finally {
            out.close()
            return false
        }
    }

    /**
     * 增加漏洞txt的行业
     * @param args
     */
    def test10 = {
        println "CreateTxtJob执行" + new Date()
        SimpleDateFormat sd = new SimpleDateFormat("yyyy")
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        //昨天天数
//		Calendar calendar = Calendar.getInstance();
//		calendar.setTime(new Date());
//		calendar.add(Calendar.DAY_OF_MONTH, -1);
//		Date date = sdf.parse(sdf.format(calendar.getTime()));
//		println sdf.format(calendar.getTime())
        String ss = sd.format(new Date())
        Date date = sdf.parse("2017-10-18");
//		Date nowDate = sdf.parse("2017-11-09");
//		println sd.format(new Date())

        //当前天数
        Date nowDate = sdf.parse(sdf.format(new Date()));
        println sdf.format(new Date())
        //漏洞表查询
        //IndustryFlaw.executeQuery('select distinct iflaw.industry from IndustryFlaw iflaw where iflaw.flaw = ?',[flawInstance])
//		def hql = "from Flaw t where t.status = 9 and t.enable=1 and t.parentFlaw is null and t.storageTime >= :storageTime and t.storageTime < :nowDate";
        def hql = "from IndustryFlaw t where t.dateCreated < :nowDate";
        def hqlPara = new HashMap();
//		hqlPara.put("storageTime", date)
        hqlPara.put("nowDate", nowDate)
        hql += " order by t.dateCreated desc"
        def IndustryFlawList = IndustryFlaw.executeQuery(hql, hqlPara)

        //定义txt内容
        String industryTxt = "";
        for (IndustryFlaw industryFlawInstance : IndustryFlawList) {
            try {
                industryTxt += (industryFlawInstance.flaw.id + "&&&&&" + industryFlawInstance.industry.id + "&&&&&" + industryFlawInstance.industry.name).replaceAll("\r|\n|\t|\r\n", "") + "\r\n";
            } catch (Exception e) {
                continue;
                //e.printStackTrace()
            }
        }
//		String industryPath = "/cnvd/TXT/industry";
        String industryPath = "${config.filePath.TxtIndustryPath}";
        println industryPath
        String industryName = "industry_2017-11-28.txt";
        FilesUtil.writeTxt(industryPath, industryName, industryTxt)
        println "生成txt结束"
    }

    /**
     * 域名提取
     * @param args
     */
    def test11 = {
        //238854 id大于
        String sql = "SELECT t.id FROM flaw_url t;"
        def adnList = Alldomainname.findAll()
        def idList = SQLUtil.getList(sql)
        TopDomainUtil icp1 = new TopDomainUtil();
        for (String fid : idList) {
            FlawUrl fu = FlawUrl.get(fid.id)
            try {
                if (fu.flaw) {
                    if (fu.url && !"".equals(fu.url)) {
                        URL url1 = new URL(fu.url);
                        System.out.println(url1.getHost());
                        String damain = icp1.getTopDomain(adnList, url1.getHost())
                        println damain
                        def fdBak = FlawDomain.findAllByFlawAndDomain(fu.flaw, damain.trim())
                        if (!fdBak) {
                            FlawDomain fd = new FlawDomain()
                            fd.flaw = fu.flaw
                            fd.domain = damain.trim()
                            if (!fd.save(flush: true)) {
                                fd.errors.allErrors.each { println it }
                                continue;
                            }
                        }
                        println "=========================="
                    }
                }
            } catch (Exception e) {
                println "错误url" + fu.url
                continue;
//				e.printStackTrace()
            }
        }
        println "输出成功====================="
    }

    public static void main(String[] args) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date date = calendar.getTime();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
        String storageTimeStr = sdf.format(date);
        println storageTimeStr
        String filePath = "E:/cnvd/TXT";
        String fileName = "addfile1.txt";
        String content = "这是一个测试工具\r\n第二行\r\n再次测试";
        FilesUtil.writeTxt(filePath, fileName, content)
//		String url = "http://**************:8881/shoppingAdmin/pushTuser/sendTuser"
//		String aes = "5onI4D8AeDx3D5VbuurGMiOS9Q/WRJXioCBTwZsxaq5WsVvpdw6KQHESX7F6gFbmn7sjG4cNwlO5Qv7EcONRJU/2kG5JKinluIOQJbvYzYYloR0Phu1TDPtu0oPJ+NalccG1j9w4Vi9r96I6FRHdg38UkvTFY7dFHWSPYlxAj6E="
//		int str = HttpClientUtil.send(aes, url);
//		println str
//		def apiurl = "http://localhost:8777/createVitualMachine?imageUuid=be5284095b2e4c8988fa46cb997c2974"
//		def resstr = "" ;
//		URL url = new URL(apiurl);
//		java.net.HttpURLConnection httpurlconnection = (java.net.HttpURLConnection) url.openConnection();
//		httpurlconnection.setDoOutput(true);
//		httpurlconnection.setRequestMethod("POST");
//
//		java.io.BufferedReader inb;
//		try {
//			inb = new java.io.BufferedReader(new java.io.InputStreamReader(httpurlconnection.getInputStream()));
//		println "获取任务连接成功============="
//			} catch (Exception e) {
//			 e.printStackTrace();
//			println "获取任务详细失败"
//		}
//		String line;
//		while ((line = inb.readLine()) != null) {
//			resstr += line;
//		}
//		inb.close();
//		println resstr

    }
}
