package com.cnvd.service

import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

import com.cnvd.common.Attachment

class AttachmentService {

	static transactional = true

	def uploadFile(def file,def filePath,def realName){
		Attachment attachment=null

		try{
			File pathFile = new File(filePath)
			if(!pathFile.exists()){
				pathFile.mkdirs()
			}
			String fileName= file.getOriginalFilename()
			fileName=fileName.substring(fileName.lastIndexOf("."));
			String fileUrlStr=filePath+realName+fileName
			attachment=new Attachment()
			attachment.realName=realName+fileName
			attachment.fileName=file.getOriginalFilename()
			attachment.fileType=file.getContentType()
			attachment.fileSize=file.getSize()
			attachment.path=fileUrlStr
			attachment.save()

			file.transferTo(new File(fileUrlStr));
		}catch(Exception e){
			e.printStackTrace()
			return null
		}
		return attachment
	}

	/**
	 * 下载附件
	 * @param att
	 * @param request
	 * @param response
	 * @return
	 */
	def downloadAtt(Attachment att,HttpServletRequest request,HttpServletResponse response){
		request.setCharacterEncoding("UTF-8");
		BufferedInputStream bis = null;
		BufferedOutputStream bos = null;

		String downLoadPath = att.path;
		long fileLength = new File(downLoadPath).length();
		def browserType = request.getHeader("User-Agent")
		response.setContentType("application/octet-stream");
		byte[] bytes = browserType.contains("MSIE") ? att.fileName.getBytes() : att.fileName.getBytes("utf-8");
		response.setHeader("Content-disposition", "attachment; filename="+new String(bytes, "ISO-8859-1"));
		response.setHeader("Content-Length", String.valueOf(fileLength));

		bis = new BufferedInputStream(new FileInputStream(downLoadPath));
		bos = new BufferedOutputStream(response.getOutputStream());
		byte[] buff = new byte[2048];
		int bytesRead;
		while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
			bos.write(buff, 0, bytesRead);
		}
		bis.close();
		bos.close();
	}
	
	def checkFile(def file){
		def fileName = file.getOriginalFilename()
		def fileNameSuffix=fileName.substring(fileName.lastIndexOf("."))
		println "contentType="+file.getContentType()
		
		if(".zip".equals(fileNameSuffix) && ("application/x-zip-compressed".equals(file.getContentType())||"application/zip".equals(file.getContentType()))){
			return true
		}else{
			return false
		}
	}
}
