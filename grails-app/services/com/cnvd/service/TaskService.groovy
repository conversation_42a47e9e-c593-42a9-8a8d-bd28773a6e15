package com.cnvd.service

import com.cnvd.common.Attachment
import com.cnvd.common.FlawApply
import com.cnvd.common.FlawApplyAttachment
import com.cnvd.common.Task
import com.cnvd.flawInfo.Exploit
import com.cnvd.flawInfo.ExploitAttachment
import com.cnvd.flawInfo.FlawProcess
import com.cnvd.flawInfo.ProcessInfo
import com.cnvd.patchInfo.PatchInfo
import com.cnvd.patchInfo.PatchInfoAttachment
import com.cnvd.util.Constants

class TaskService {

    static transactional = true
	def attachmentService
	def grailsApplication
    def submitTask(def params,def file) {
		println params.id+"pppppppppppp"
		println "params.attIds="+params.attIds
		def taskInstance = Task.get(params.id)
	
		if(taskInstance.status!=Constants.TASK_SUBMIT && taskInstance.status!=Constants.TASK_BACK && taskInstance.status!=Constants.TASK_ING){
			return false;
		}
		if(taskInstance.type==Constants.FLAW_APPLY){
			FlawApply flawApply=null
			if(taskInstance.status==Constants.TASK_SUBMIT || taskInstance.status==Constants.TASK_BACK){
				flawApply=taskInstance.flawApply
				/*if(params.isDeleteAtt!=null && params.int('isDeleteAtt')==1){
					flawApply.attachment=null
				}*/
				flawApply.properties=params
			}else{
				flawApply=new FlawApply(params)
			}
			/*if(file!=null ){
				if(checkFile(file.getContentType())){
					return false
				}
				//表示附件有内容
				String filePath = "${grailsApplication.config.filePath.flawApplyAttFilePath}" //文件的路径
				String realName = DateUtils.getCurrentTime() //文件的真实文件名
				def attachment = attachmentService.uploadFile(file,filePath,realName)
				flawApply.attachment = attachment
			}*/
			taskInstance.status=Constants.TASK_SUBMIT
			taskInstance.submitDate=new Date()
			
			flawApply.flaw=taskInstance.flaw
			if(flawApply.save(flush:true)){
				FlawApplyAttachment.executeUpdate("delete from FlawApplyAttachment where flawApply = ?",[flawApply])
				if(params.attIds){
					def arr = params.attIds.split(";")
					arr.each{
						def attachment = Attachment.get(it)
						def flawApplyAttachment = new FlawApplyAttachment()
						flawApplyAttachment.flawApply = flawApply
						flawApplyAttachment.attachment = attachment
						flawApplyAttachment.save(flush:true)
					}
				}
			}
			taskInstance.flawApply=flawApply
			taskInstance.save()
		}else if(taskInstance.type==Constants.FLAW_EXPOIT){
			println "params.attIds="+params.attIds
			Exploit exploit=null
			if(taskInstance.status==Constants.TASK_SUBMIT || taskInstance.status==Constants.TASK_BACK){
				exploit=taskInstance.exploit
				/*if(params.isDeleteAtt!=null && params.int('isDeleteAtt')==1){
					exploit.attachment=null
				}*/
				exploit.properties=params
			}else{
				exploit=new Exploit(params)
				exploit.exploitType=taskInstance.getTargetType()
				exploit.user=taskInstance.getTargetUser()
				exploit.tuser=taskInstance.getTargetTUser()
				exploit.flaw=taskInstance.getFlaw()
				exploit.status=Constants.EXPLOIT_WAIT
				ProcessInfo  processInfo =ProcessInfo.get(Constants.tjyz)
				new FlawProcess(processInfo:processInfo,info:Constants.rwyztgStr,flaw:exploit.flaw,user:taskInstance.getTargetUser()).save()
			}
			
			/*if(file!=null ){
				if(checkFile(file.getContentType())){
					return false
				}
				//表示附件有内容filePath.exploitAttFilePath
				String filePath = "${grailsApplication.config.filePath.exploitAttFilePath}" //文件的路径
				String realName = DateUtils.getCurrentTime() //文件的真实文件名
				def attachment = attachmentService.uploadFile(file,filePath,realName)
				exploit.attachment = attachment
			}*/
			taskInstance.status=Constants.TASK_SUBMIT
			taskInstance.submitDate=new Date()
			if(exploit.save(flush:true)){
				//删除验证之前的所有附件，并重新添加新的
				ExploitAttachment.executeUpdate("delete from ExploitAttachment where exploit = ?",[exploit]);
				if(params.attIds){
					def attIdArr = params.attIds.split(";")
					attIdArr.each{
						def attachment = Attachment.get(it)
						def exploitAttachment = new ExploitAttachment()
						exploitAttachment.exploit = exploit
						exploitAttachment.attachment = attachment
						exploitAttachment.save(flush:true)
					}
				}
			}
			taskInstance.exploit=exploit
			taskInstance.save()
			
		}else if(taskInstance.type==Constants.FLAW_PATCH){
			PatchInfo patchInfo=null
			println "flawPatchInfo attIds = "+params.attIds
			if(taskInstance.status==Constants.TASK_SUBMIT || taskInstance.status==Constants.TASK_BACK){
				patchInfo=taskInstance.patchInfo
				patchInfo.properties=params
			}else{
				patchInfo=new PatchInfo(params)
				patchInfo.createSource=taskInstance.getTargetType().toString()
				patchInfo.user=taskInstance.getTargetUser()
				patchInfo.tuser=taskInstance.getTargetTUser()
				patchInfo.flaw=taskInstance.getFlaw()
				patchInfo.status=Constants.PATCH_WAIT
				ProcessInfo  processInfo =ProcessInfo.get(Constants.tjcz)
				new FlawProcess(processInfo:processInfo,info:Constants.rwcztgStr,flaw:patchInfo.flaw,user:taskInstance.getTargetUser()).save()
			}
			 
			/*if(file!=null ){
				if(checkFile(file.getContentType())){
					return false
				}
				//表示附件有内容filePath.exploitAttFilePathfilePath.patchInfoAttFilePath
				String filePath = "${grailsApplication.config.filePath.patchInfoAttFilePath}" //文件的路径
				String realName = DateUtils.getCurrentTime() //文件的真实文件名
				def attachment = attachmentService.uploadFile(file,filePath,realName)
				patchInfo.attachment = attachment
			}*/
			taskInstance.status=Constants.TASK_SUBMIT
			taskInstance.submitDate=new Date()
			if(patchInfo.save(flush:true)){
				PatchInfoAttachment.executeUpdate('delete from PatchInfoAttachment where patchInfo = ?',[patchInfo])
				if(params.attIds){
					def attIdArr = params.attIds.split(";");
					attIdArr.each{
						def attachment = Attachment.get(it)
						def patchInfoAttachment = new PatchInfoAttachment()
						patchInfoAttachment.patchInfo = patchInfo
						patchInfoAttachment.attachment = attachment
						patchInfoAttachment.save(flush:true)
					}
				}
			}
			taskInstance.patchInfo=patchInfo
			taskInstance.save()
		}
		return true
    }
	def checkFile(def fileType){
		
		if(!"application/octet-stream".equals(fileType) || !"application/x-rar-compressed".equals(fileType) || !"application/x-zip-compressed".equals(fileType)){
			return false
		}
		return true
		
	}
}
