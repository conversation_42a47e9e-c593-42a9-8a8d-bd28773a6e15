package com.cnvd.service

import com.cnvd.IntegralInfo
import com.cnvd.TUser

class UserService {

    static transactional = true

    def addUserScore(TUser tuser,Integer scoreType,Integer scoreValue) {
		tuser.integValue=tuser.integValue ? tuser.integValue+scoreValue : 0 +scoreValue
		tuser.save(flush:true)
		def integralInfo=new IntegralInfo()
		integralInfo.user=tuser
		integralInfo.integralType=scoreType
		integralInfo.integralValue=scoreValue
		integralInfo.save(flush:true)
		if(tuser.userType == 100200 && tuser.unitMem){
			def unitMem = tuser.unitMem
			unitMem.integValue=unitMem.integValue ? unitMem.integValue+scoreValue : 0 +scoreValue
			unitMem.save(flush:true)
			def integralInfo2=new IntegralInfo()
			integralInfo2.user=unitMem
			integralInfo2.integralType=scoreType
			integralInfo2.integralValue=scoreValue
			integralInfo2.save(flush:true)
		}
    }
}
