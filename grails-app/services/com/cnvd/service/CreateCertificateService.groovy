package com.cnvd.service

import java.awt.Font
import java.text.SimpleDateFormat

import com.cnvd.flawInfo.Certificate
import com.cnvd.util.DateUtils
import com.itextpdf.text.DocumentException
import com.itextpdf.text.Element
import com.itextpdf.text.Phrase
import com.itextpdf.text.pdf.BaseFont
import com.itextpdf.text.pdf.ColumnText
import com.itextpdf.text.pdf.PdfContentByte
import com.itextpdf.text.pdf.PdfReader
import com.itextpdf.text.pdf.PdfStamper

class CreateCertificateService {

	static transactional = true

	/**
	 * 创建漏洞报送证书pdf
	 * @return
	 */
	def createBSCerPdf(String templateFilePath,Certificate cert,String count,String reportTime) {
		String fileRealPath = cert.pdfAttachment.path
		try {
			PdfReader pd = new PdfReader(templateFilePath);
			String filePath = cert.pdfAttachment.path
			filePath = filePath.substring(0,filePath.lastIndexOf("/"))
			File file = new File(filePath);// 先创建目录
			if (!file.exists()) {
				file.mkdirs();
			}
			PdfStamper ps = new PdfStamper(pd, new FileOutputStream(fileRealPath));
			ps.setRotateContents(true);
			PdfContentByte cb = ps.getOverContent(1);
			com.itextpdf.text.pdf.BaseFont bf = com.itextpdf.text.pdf.BaseFont.createFont("com/itextpdf/text/pdf/fonts/simsun.ttc,1",
					BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
			float start_x = 101;
			float start_y = 475;
			float h_y = 16;
			float title_width = 120;
			float cavas_width = 465;
			float max_height = 75;
			float jiange = 22f;
			float s_x = 101;

			/**
			 * 设置报送单位
			 */
			ColumnText ct = new ColumnText(cb);
			Phrase pp = new Phrase("报送单位：", new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp, s_x, start_y, (float)(start_x + title_width), (float)(start_y - max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			s_x = ct.getLastX();
			String contributorcompany = cert.tuser.userName;// 留言
			Phrase pp1 = new Phrase(contributorcompany,
					new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp1, s_x, start_y, cavas_width, (float)(start_y - max_height), jiange, Element.ALIGN_LEFT);
			ct.go();

			/**
			 * 设置报送时间
			 */
			System.out.println("yLine=" + ct.getYLine());
			start_y = ct.getYLine() - h_y;
			s_x = start_x;
			Phrase pp13 = new Phrase("报送时间：",
					new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp13, s_x, start_y, (float)(start_x + title_width),(float)(start_y-max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			s_x = ct.getLastX();
			Phrase pp12 = new Phrase(reportTime, new com.itextpdf.text.Font(bf,
					18));
			ct.setSimpleColumn(pp12, s_x, start_y, cavas_width, (float)(start_y - max_height), jiange, Element.ALIGN_LEFT);
			ct.go();

			/**
			 * 设置报送数量
			 */
			start_y = ct.getYLine() - h_y;
			s_x = start_x;
			Phrase pp2 = new Phrase("报送数量：", new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp2, s_x, start_y, (float)(start_x + title_width),
					(float)(start_y-max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			s_x = ct.getLastX();
			Phrase pp3 = new Phrase(count, new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp3, s_x, start_y, cavas_width, (float)(start_y-max_height), jiange, Element.ALIGN_LEFT);
			ct.go();

			/**
			 * 设置证书编号
			 */
			start_y = ct.getYLine() - h_y;
			s_x = start_x;
			Phrase pp4 = new Phrase("证书编号：", new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp4, s_x, start_y, (float)(start_x + title_width),
					(float)(start_y - max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			s_x = ct.getLastX();
			Phrase pp5 = new Phrase(cert.c_id,
					new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp5, s_x, start_y, cavas_width, (float)(start_y-max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			
			/**
			 * 认证时间
			 */
			start_y = ct.getYLine() - h_y;
			s_x = start_x;
			Phrase pp40 = new Phrase("认证时间：", new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp40, s_x, start_y, (float)(start_x + title_width),
					(float)(start_y - max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			s_x = ct.getLastX();
			Phrase pp41 = new Phrase(new SimpleDateFormat("yyyy年MM月dd日").format(cert.awardTime),
					new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp41, s_x, start_y, cavas_width, (float)(start_y-max_height), jiange, Element.ALIGN_LEFT);
			ct.go();

			/**
			 * 设置发证单位及发证时间
			 */
			start_y = ct.getYLine() - h_y;
			s_x = start_x;
			Phrase pp10 = new Phrase(cert.awardCompany,
					new com.itextpdf.text.Font(bf, 18, Font.BOLD));
			ct.setSimpleColumn(pp10, 162, 160, 475, (float)(162 - max_height), jiange,
					Element.ALIGN_LEFT);
			ct.go();
			/*start_y = ct.getYLine() - h_y;
			Phrase pp11 = new Phrase(DateUtils.parseToChNDate(cert.awardTime), new com.itextpdf.text.Font(
					bf, 18, Font.BOLD));
			ct.setSimpleColumn(pp11, 239, 120, cavas_width, 80, jiange,
					Element.ALIGN_LEFT);*/
			// ct.setSimpleColumn(phrase, llx, lly, urx, ury, leading,
			// alignment)
			//ct.go();

			ct.clearChunks();
			ps.close();
			return true;
		} catch (IOException e) {
			e.printStackTrace();
			return false;
		} catch (DocumentException e) {
			e.printStackTrace();
			return false;
		}
	}
	
	/**
	 * 创建原创证书pdf
	 * @return
	 */
	def createYCCerPdf(String templateFilePath,Certificate cert,String flawRisk,String name,String workplace){
		String fileRealPath = cert.pdfAttachment.path
		try {
			PdfReader pd = new PdfReader(templateFilePath);
			String filePath = cert.pdfAttachment.path
			filePath = filePath.substring(0,filePath.lastIndexOf("/"))
			File file = new File(filePath);// 先创建目录
			if (!file.exists()) {
				file.mkdirs();
			}
			PdfStamper ps = new PdfStamper(pd, new FileOutputStream(fileRealPath));
			ps.setRotateContents(true);
			PdfContentByte cb = ps.getOverContent(1);
			BaseFont bf = BaseFont.createFont("com/itextpdf/text/pdf/fonts/simsun.ttc,1",
					BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
			float start_x = 101;
			float start_y = 475;
			float h_y = 16;
			float title_width = 120;
			float cavas_width = 465;
			float max_height = 75;
			float jiange = 22f;
			float s_x = 101;
			com.itextpdf.text.Font myFont = new com.itextpdf.text.Font(bf, 18);

			myFont.setColor(35, 24, 21);

			// myFont.setSize(0.2f);

			ColumnText ct = new ColumnText(cb);

			Phrase pp = new Phrase("漏洞编号：", myFont);

			ct.setSimpleColumn(pp, s_x, start_y, (float)(start_x + title_width), (float)(start_y- max_height), jiange, Element.ALIGN_LEFT);

			ct.go();

			s_x = ct.getLastX();

			Phrase pp1 = new Phrase(cert.flaw.number, myFont);
			ct.setSimpleColumn(pp1, s_x, start_y, cavas_width, (float)(start_y- max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			// ct.clearChunks();
			start_y = ct.getYLine() - h_y;
			s_x = start_x;
			Phrase pp13 = new Phrase("漏洞名称：",
					new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp13, s_x, start_y, (float)(start_x + title_width),
					(float)(start_y - max_height), jiange, Element.ALIGN_LEFT);
			ct.go();

			s_x = ct.getLastX();
			Phrase pp12 = new Phrase(cert.flaw.title, new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp12, s_x, start_y, cavas_width, (float)(start_y- max_height), jiange, Element.ALIGN_LEFT);
			ct.go();

			start_y = ct.getYLine() - h_y;
			s_x = start_x;

			Phrase pp2 = new Phrase("漏洞类型：", new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp2, s_x, start_y, (float)(start_x + title_width),
					(float)(start_y - max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			s_x = ct.getLastX();
			Phrase pp3 = new Phrase(cert.leakType.name+"-"+flawRisk,
					new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp3, s_x, start_y, cavas_width, (float)(start_y - max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			
			/*start_y = ct.getYLine() - h_y;
			s_x = start_x;

			Phrase ppx = new Phrase("漏洞风险：", new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(ppx, s_x, start_y, (float)(start_x + title_width),
					(float)(start_y - max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			s_x = ct.getLastX();
			Phrase ppy = new Phrase(flawRisk,
					new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(ppy, s_x, start_y, cavas_width, (float)(start_y
					- max_height), jiange, Element.ALIGN_LEFT);
			ct.go();*/
			
			
			// ct.clearChunks();
			start_y = ct.getYLine() - h_y;
			s_x = start_x;
			Phrase pp4 = new Phrase("贡 献 者：",
					new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp4, s_x, start_y, (float)(start_x + title_width),
					(float)(start_y - max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			s_x = ct.getLastX();
			Phrase pp5 = new Phrase(name,
					new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp5, s_x, start_y, cavas_width, (float)(start_y
					- max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			// ct.clearChunks();
			start_y = ct.getYLine() - h_y;
			s_x = start_x;
			Phrase pp6 = new Phrase("贡献者单位：",
					new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp6, s_x, start_y, (float)(start_x + title_width),
					(float)(start_y - max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			s_x = ct.getLastX();
			Phrase pp7 = new Phrase(workplace, new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp7, s_x, start_y, cavas_width, (float)(start_y
					- max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			
			start_y = ct.getYLine() - h_y;
			s_x = start_x;
			Phrase pp8 = new Phrase("证书编号：", new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp8, s_x, start_y, (float)(start_x + title_width),
					(float)(start_y - max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			s_x = ct.getLastX();

			Phrase pp9 = new Phrase(cert.c_id, new com.itextpdf.text.Font(bf,
					18));
			ct.setSimpleColumn(pp9, s_x, start_y, cavas_width, (float)(start_y
					- max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			
			
			start_y = ct.getYLine() - h_y;
			s_x = start_x;
			Phrase pp10 = new Phrase("收录时间：", new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp10, s_x, start_y, (float)(start_x + title_width),
					(float)(start_y - max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			s_x = ct.getLastX();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
			Phrase pp11 = new Phrase(sdf.format(cert.awardTime), new com.itextpdf.text.Font(bf,
					18));
			ct.setSimpleColumn(pp11, s_x, start_y, cavas_width, (float)(start_y
					- max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			
			
			
			start_y = ct.getYLine() - h_y;
			Phrase pp15 = new Phrase(cert.awardCompany,
					new com.itextpdf.text.Font(bf, 18, Font.BOLD));
			ct.setSimpleColumn(pp15, 162, 125, 475, (float)(127 - max_height), jiange,
					Element.ALIGN_LEFT);
			ct.go();

			ct.clearChunks();
			ps.close();
			return true;
		} catch (IOException e) {
			e.printStackTrace();
			return false;
		} catch (DocumentException e) {
			e.printStackTrace();
			return false;
		}
	}
	
	
	
	def createYCCerPdf2(String templateFilePath,String fileRealPath,
		String flawNumber,String flawTitle,String leakName,String certNumber,
		String certAwardTime,String certAwardCompany,
		String flawRisk,String name,String workplace){
		try {
			PdfReader pd = new PdfReader(templateFilePath);
			String filePath = fileRealPath.substring(0,fileRealPath.lastIndexOf("/"))
			File file = new File(filePath);// 先创建目录
			if (!file.exists()) {
				file.mkdirs();
			}
			PdfStamper ps = new PdfStamper(pd, new FileOutputStream(fileRealPath));
			ps.setRotateContents(true);
			PdfContentByte cb = ps.getOverContent(1);
			BaseFont bf = BaseFont.createFont("com/itextpdf/text/pdf/fonts/simsun.ttc,1",
					BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
			float start_x = 101;
			float start_y = 475;
			float h_y = 16;
			float title_width = 120;
			float cavas_width = 465;
			float max_height = 75;
			float jiange = 22f;
			float s_x = 101;
			com.itextpdf.text.Font myFont = new com.itextpdf.text.Font(bf, 18);

			myFont.setColor(35, 24, 21);

			// myFont.setSize(0.2f);

			ColumnText ct = new ColumnText(cb);

			Phrase pp = new Phrase("漏洞编号：", myFont);

			ct.setSimpleColumn(pp, s_x, start_y, (float)(start_x + title_width), (float)(start_y- max_height), jiange, Element.ALIGN_LEFT);

			ct.go();

			s_x = ct.getLastX();

			Phrase pp1 = new Phrase(flawNumber, myFont);
			ct.setSimpleColumn(pp1, s_x, start_y, cavas_width, (float)(start_y- max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			// ct.clearChunks();
			start_y = ct.getYLine() - h_y;
			s_x = start_x;
			Phrase pp13 = new Phrase("漏洞名称：",
					new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp13, s_x, start_y, (float)(start_x + title_width),
					(float)(start_y - max_height), jiange, Element.ALIGN_LEFT);
			ct.go();

			s_x = ct.getLastX();
			Phrase pp12 = new Phrase(flawTitle, new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp12, s_x, start_y, cavas_width, (float)(start_y- max_height), jiange, Element.ALIGN_LEFT);
			ct.go();

			/*start_y = ct.getYLine() - h_y;
			s_x = start_x;
			Phrase pp2 = new Phrase("漏洞类型：", new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp2, s_x, start_y, (float)(start_x + title_width),
					(float)(start_y - max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			s_x = ct.getLastX();
			Phrase pp3 = new Phrase(leakName+"-"+flawRisk,
					new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp3, s_x, start_y, cavas_width, (float)(start_y - max_height), jiange, Element.ALIGN_LEFT);
			ct.go();*/
			
			start_y = ct.getYLine() - h_y;
			s_x = start_x;
			Phrase pp4 = new Phrase("贡 献 者：",
					new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp4, s_x, start_y, (float)(start_x + title_width),
					(float)(start_y - max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			s_x = ct.getLastX();
			Phrase pp5 = new Phrase(name,
					new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp5, s_x, start_y, cavas_width, (float)(start_y
					- max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			// ct.clearChunks();
			start_y = ct.getYLine() - h_y;
			s_x = start_x;
			Phrase pp6 = new Phrase("贡献者单位：",
					new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp6, s_x, start_y, (float)(start_x + title_width),
					(float)(start_y - max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			s_x = ct.getLastX();
			Phrase pp7 = new Phrase(workplace, new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp7, s_x, start_y, cavas_width, (float)(start_y
					- max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			
			start_y = ct.getYLine() - h_y;
			s_x = start_x;
			Phrase pp8 = new Phrase("证书编号：", new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp8, s_x, start_y, (float)(start_x + title_width),
					(float)(start_y - max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			s_x = ct.getLastX();

			Phrase pp9 = new Phrase(certNumber, new com.itextpdf.text.Font(bf,
					18));
			ct.setSimpleColumn(pp9, s_x, start_y, cavas_width, (float)(start_y
					- max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			
			
			/*start_y = ct.getYLine() - h_y;
			s_x = start_x;
			Phrase pp10 = new Phrase("收录时间：", new com.itextpdf.text.Font(bf, 18));
			ct.setSimpleColumn(pp10, s_x, start_y, (float)(start_x + title_width),
					(float)(start_y - max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			s_x = ct.getLastX();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
			Phrase pp11 = new Phrase(certAwardTime, new com.itextpdf.text.Font(bf,
					18));
			ct.setSimpleColumn(pp11, s_x, start_y, cavas_width, (float)(start_y
					- max_height), jiange, Element.ALIGN_LEFT);
			ct.go();
			*/
			
			
			start_y = ct.getYLine() - h_y;
			Phrase pp15 = new Phrase(certAwardCompany,
					new com.itextpdf.text.Font(bf, 18, Font.BOLD));
			ct.setSimpleColumn(pp15, 162, 125, 475, (float)(127 - max_height), jiange,
					Element.ALIGN_LEFT);
			ct.go();

			ct.clearChunks();
			ps.close();
			return true;
		} catch (IOException e) {
			e.printStackTrace();
			return false;
		} catch (DocumentException e) {
			e.printStackTrace();
			return false;
		}
	}
	public static void main(String[] args) {
		CreateCertificateService ccfs = new CreateCertificateService()
		
		/*String templateFilePath,String fileRealPath,
		String flawNumber,String flawTitle,String leakName,String certNumber,
		String certAwardTime,String certAwardCompany,
		String flawRisk,String name,String workplace*/
		
		String templateFilePath = "e:/newCert/ycmb.pdf"
		String fileRealPath = "e:/newCert/CNVD-YC-201202069274.pdf"
		String flawNumber = "CNVD-2012-10328"
		String flawTitle = "用友软件ICC客服系统文件上传漏洞"
		String leakName = "通用—web应用"
		String certNumber = "CNVD-YC-201202069274"
		String certAwardTime = "2012年2月28日"
		String certAwardCompany = "国家计算机网络应急技术处理协调中心"
		String flawRisk = "高危"
		String name = "张瑞冬"
		String workplace = "四川无声信息技术有限公司"
		
		ccfs.createYCCerPdf2(templateFilePath, fileRealPath, flawNumber, flawTitle, leakName, certNumber, certAwardTime, certAwardCompany, flawRisk, name, workplace)
		
		
	}
}
