package com.cnvd.service

import com.alibaba.fastjson.JSON
import com.cnvd.HttpUtil
import com.cnvd.flawInfo.Flaw
import com.cnvd.vo.Response
import org.codehaus.groovy.grails.commons.ConfigurationHolder

class FlawService {

	static transactional = true
	def static config = ConfigurationHolder.config
	def grailsApplication
	def getNumber(){
		Calendar cal=Calendar.getInstance();//使用日历类
		int year=cal.get(Calendar.YEAR);//得到年
		def hql = "select max(number) from Flaw where number like 'CNVD-"+year+"%'"
		def maxNumberStr = Flaw.executeQuery(hql)[0]
		def number = "";
		if(maxNumberStr==null){
			number="CNVD-"+year+"-"+ String.format("%05d", 1);
		}else{
			def numberInt = Integer.parseInt(maxNumberStr.substring(maxNumberStr.length()-5,maxNumberStr.length()))+1
			number="CNVD-"+year+"-"+ String.format("%05d", numberInt);
		}
		return number
	}
	def setFlawUrl(def flawId,def url) {
		try {
			Map<String, Object> paramsValue = new HashMap<String, Object>()
			paramsValue.put("flawId", flawId)
			paramsValue.put("url", url)
			def flawUrlSaveInterface ="${grailsApplication.config.url.saveInterface}"
			//log.info("flawUrlSaveInterface|"+flawUrlSaveInterface)
			//log.info("paramsValue|"+paramsValue.toString())
			String responseStr = HttpUtil.doPost(flawUrlSaveInterface, paramsValue)
			Response response = JSON.parseObject(responseStr, Response.class)
			if (Response.SUCCESS == response.getCode()) {
			} else {
				log.error("setFlawUrl失败|" + response.getMessage())
			}
		}catch(Exception e){
			log.error("setFlawUrl失败|",e)
		}
	}
}
