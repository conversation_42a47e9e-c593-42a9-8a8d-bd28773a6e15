#
# Translated by <PERSON>@gmail.com
#

default.doesnt.match.message=O campo [{0}] da classe [{1}] com o valor [{2}] não atende ao padrão definido [{3}]
default.invalid.url.message=O campo [{0}] da classe [{1}] com o valor [{2}] não é uma URL válida
default.invalid.creditCard.message=O campo [{0}] da classe [{1}] com o valor [{2}] não é um número válido de cartão de crédito
default.invalid.email.message=O campo [{0}] da classe [{1}] com o valor [{2}] não é um endereço de email válido.
default.invalid.range.message=O campo [{0}] da classe [{1}] com o valor [{2}] não está entre a faixa de valores válida de [{3}] até [{4}]
default.invalid.size.message=O campo [{0}] da classe [{1}] com o valor [{2}] não está na faixa de tamanho válida de [{3}] até [{4}]
default.invalid.max.message=O campo [{0}] da classe [{1}] com o valor [{2}] ultrapass o valor máximo [{3}]
default.invalid.min.message=O campo [{0}] da classe [{1}] com o valor [{2}] não atinge o valor mínimo [{3}]
default.invalid.max.size.message=O campo [{0}] da classe [{1}] com o valor [{2}] ultrapassa o tamanho máximo de [{3}]
default.invalid.min.size.message=O campo [{0}] da classe [{1}] com o valor [{2}] não atinge o tamanho mínimo de [{3}]
default.invalid.validator.message=O campo [{0}] da classe [{1}] com o valor [{2}] não passou na validação
default.not.inlist.message=O campo [{0}] da classe [{1}] com o valor [{2}] não é um valor dentre os permitidos na lista [{3}]
default.blank.message=O campo [{0}] da classe [{1}] não pode ficar em branco
default.not.equal.message=O campo [{0}] da classe [{1}] com o valor [{2}] não pode ser igual a [{3}]
default.null.message=O campo [{0}] da classe [{1}] não pode ser vazia
default.not.unique.message=O campo [{0}] da classe [{1}] com o valor [{2}] deve ser único

default.paginate.prev=Anterior
default.paginate.next=Próximo

# Mensagens de erro em atribuição de valores. Use "typeMismatch.$className.$propertyName" para customizar (eg typeMismatch.Book.author)
typeMismatch.java.net.URL=O campo {0} deve ser uma URL válida.
typeMismatch.java.net.URI=O campo {0} deve ser uma URI válida.
typeMismatch.java.util.Date=O campo {0} deve ser uma data válida
typeMismatch.java.lang.Double=O campo {0} deve ser um número válido.
typeMismatch.java.lang.Integer=O campo {0} deve ser um número válido.
typeMismatch.java.lang.Long=O campo {0} deve ser um número válido.
typeMismatch.java.lang.Short=O campo {0} deve ser um número válido.
typeMismatch.java.math.BigDecimal=O campo {0} deve ser um número válido.
typeMismatch.java.math.BigInteger=O campo {0} deve ser um número válido.