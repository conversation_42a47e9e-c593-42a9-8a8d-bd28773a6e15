package cnvd_admin

import com.cnvd.MD5
import com.cnvd.TUser
import com.cnvd.api.QihooApi
import com.cnvd.common.Attachment
import com.cnvd.flawInfo.DetailedInfo
import com.cnvd.flawInfo.Flaw
import com.cnvd.flawInfo.FlawUrl
import com.cnvd.listener.QihooAll
import com.cnvd.listener.QihooOne
import com.cnvd.qihoo.HttpClientutils
import com.cnvd.qihoo.Receiver
import com.cnvd.wooyun.util.MutexLock
import com.cnvd.wooyun.util.Util
import com.cnvd.wooyun.util.WordUtil
import net.sf.json.JSONArray
import net.sf.json.JSONObject
import org.apache.commons.io.IOUtils
import org.codehaus.groovy.grails.commons.ConfigurationHolder
import org.springframework.util.FileCopyUtils

import java.nio.channels.FileLock
import java.text.SimpleDateFormat
import java.util.regex.Matcher
import java.util.regex.Pattern

class QihooJob {
    //修改为全量接口 ，因此6小时跑一次
//    def timeout = 21600000l
    static triggers ={
        //simple name:'simpleTrigger', startDelay:0,repeatInterval: 36000000, repeatCount: -1
        cron name:'cronTriggerQihooJob', cronExpression:'0 0 05 * * ?' //每天5点执行
        //cron name:'cronTriggerQihooJob', cronExpression:'0 * * * * ?'
    }
    //每7天跑一次
    //def timeout = 604800000l
    def attachmentService
    def flawService
    def grailsApplication
    def static SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
    def static SimpleDateFormat sdf = new SimpleDateFormat("yyyy")

    def static config = ConfigurationHolder.config
    def static SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
    /*private static String getTimeStamp = "https://butian.360.cn/newApi/getTimeStamp";//获取目标服务器时间戳
    private static String getNewApi = "https://butian.360.cn/newApi";//补天获取数据接口*/
    private static String getTimeStamp = "https://api.butian.net/Interface/getTimeStamp";//获取目标服务器时间戳
    private static String getNewApi = "https://api.butian.net/Interface";//补天获取数据接口
    private final static String key = "EE5065448620E4386EE8F5DDC3F97CB8"; //key值
    private static final MutexLock mutexLock = new MutexLock();
    def execute() {
        if (mutexLock.isLocked()){
            println "已经被锁定，请重试"
            return ;
        }
        mutexLock.lock();
        println "执行代码"

        println "qihoo  job  start time = " + formatter.format(new Date())
        String t1 = "qihoo" + System.currentTimeMillis();  //生成批次号
        //获取目标服务器时间戳
        String requestMethod = "POST";
        String outputStr = null;
        String timestamp;
        try {
            //timestamp = RequestUtil.httpsRequest(getTimeStamp, requestMethod,outputStr);
            timestamp = HttpClientutils.doPostSSLToJson(getTimeStamp, new JSONObject());
            println "时间戳=" + timestamp
        } catch (Exception e) {
            e.printStackTrace()
            //时间戳获取接口出错
            QihooAll qihooalls = new QihooAll()
            qihooalls.dateCreated = new Date()
            qihooalls.num = t1
            qihooalls.status = "error,获取奇虎时间戳接口访问出错!"
            qihooalls.save(flush: true)
            if (mutexLock.isLocked()){
                mutexLock.unlock()
            }
            return;
        }
        //判断是否有返回值
        if (timestamp == null || "".equals(timestamp)) {
            println "时间戳获取错误"
            //获取漏洞信息接口出错
            QihooAll qihooalls = new QihooAll()
            qihooalls.dateCreated = new Date()
            qihooalls.num = t1
            qihooalls.status = "error,获取奇虎时间戳接口访问出错!"
            qihooalls.save(flush: true)
            if (mutexLock.isLocked()){
                mutexLock.unlock()
            }
            return;
        }
        //调用数据获取接口
        String idStr = 1;
        //起始时间（UNIX时间戳格式），北京时间  eg:2017-01-01
//		String startTimeStr = sf.format(new Date())
//		Date ss = sf.parse(startTimeStr)
//		long ff = sf.parse(startTimeStr).getTime()
//		String start_time = String.valueOf(sf.parse(startTimeStr).getTime() / 1000L);
        //应补天接口方要求，日期为3天之内的 20191107 我方默认设置成4天内的数据了
        Calendar cStar = Calendar.getInstance();
        cStar.add(Calendar.DAY_OF_MONTH, -1);
        String startTimeStr = sf.format(cStar.getTime())
        def starttime="${grailsApplication.config.url.starttime}"
        if(starttime){
            startTimeStr=starttime//默认取当前时间，如果从新获取之前的数据，更改配置文件的starttime开始时间
        }
        String start_time = String.valueOf(sf.parse(startTimeStr).getTime() / 1000L);

        //截止时间（UNIX时间戳格式），北京时间  eg:2017-01-02
        //当前日期加一天
        Calendar c = Calendar.getInstance();
        String endTimeStr = sf.format(c.getTime())

//        def endtime="${grailsApplication.config.url.endtime}"
//        if(endtime){
//            endTimeStr = endtime//默认取当前时间，如果从新获取之前的数据，更改配置文件的starttime开始时间
//        }

        String end_time = String.valueOf(sf.parse(endTimeStr).getTime() / 1000L);

        //key,start_time,end_time,vulid,timestamp,id按顺序拼接。(key,timestamp,id为必填项)
        String tokenStr = key + start_time + end_time + timestamp + idStr;
        String token = MD5.getMD5Str(tokenStr).toUpperCase();
        //拼接post请求param
        String param = "id=" + idStr + "&timestamp=" + timestamp + "&token=" + token + "&start_time=" + start_time + "&end_time=" + end_time
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("id", idStr);
        params.put("timestamp", timestamp);
        params.put("token", token);
        params.put("start_time", start_time);
        params.put("end_time", end_time);
        //访问补天获取漏洞详情的接口
        String apiMethod = "POST";
        String content;
        try {
            System.out.println("startTime================"+startTimeStr+"|endTime=================="+endTimeStr);
            println "接口地址："+getNewApi + "?" + param
            // content = RequestUtil.httpsRequest(getNewApi, apiMethod, param);
            // content = HttpClientutils.doGet(getNewApi + "?" + param, new HashMap<String, Object>());
            content = HttpClientutils.newDoPost(getNewApi, param, "utf-8");
        } catch (Exception e) {
            //获取漏洞信息接口出错
            QihooAll qihooalls = new QihooAll()
            qihooalls.dateCreated = new Date()
            qihooalls.num = t1
            qihooalls.status = "error,获取奇虎漏洞接口数据出错!"
            qihooalls.save(flush: true)
            if (mutexLock.isLocked()){
                mutexLock.unlock()
            }
            return;
        }
        //判断是否有返回值
        if (content == null || "".equals(content)) {
            //获取漏洞信息接口出错
            QihooAll qihooalls = new QihooAll()
            qihooalls.dateCreated = new Date()
            qihooalls.num = t1
            qihooalls.status = "error,获取奇虎漏洞接口访问出错!"
            qihooalls.save(flush: true)
            if (mutexLock.isLocked()){
                mutexLock.unlock()
            }
            return;
        }
        //判断接口状态
//        JSONObject json = JSONObject.fromObject(content);
//        String code = json.getString("code");
//        日志
        new Receiver().log(content);
        println "job finish time = " + formatter.format(new Date())
        if (mutexLock.isLocked()){
            mutexLock.unlock()
        }

    }

    def long getFileSize(File file) {
        long s = 0;
        if (file.exists()) {
            FileInputStream fis = null;
            fis = new FileInputStream(file);
            s = fis.available();
        }
        return s;
    }
}
