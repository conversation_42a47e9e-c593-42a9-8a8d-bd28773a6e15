package cnvd_admin

import com.cnvd.MD5
import com.cnvd.TUser
import com.cnvd.UnicodeUtils
import com.cnvd.api.QihooApi
import com.cnvd.bugcloud.receive.Receiver
import com.cnvd.bugcloud.util.Util
import com.cnvd.common.Attachment
import com.cnvd.flawInfo.DetailedInfo
import com.cnvd.flawInfo.Flaw
import com.cnvd.flawInfo.FlawUrl
import com.cnvd.listener.SyncAll
import com.cnvd.listener.SyncOne
import com.cnvd.productInfo.Manufacturer
import com.cnvd.qihoo.HttpClientutils
import net.sf.json.JSONArray
import net.sf.json.JSONObject
import org.springframework.util.FileCopyUtils

import java.text.SimpleDateFormat
//
//class BugCloudJob {
//    def grailsApplication
//    def flawService
//    //job执行规则：每天凌晨1点执行
//   /* static triggers = {
//        cron name: 'myTrigger', cronExpression: '0 0 3 * * ?'
//        //cron name: 'myTrigger', cronExpression: '0 02 17 * * ?'
//    }*/
//    //漏洞类型映射关系 key为接口查询得到的，value为数据库对应
//    private static Map<Integer,Integer> flawTypeMap = new HashMap<Integer, Integer>()
//    //漏洞危害等级映射关系 key为接口查询得到的，value为数据库对应的
//    private static Map<Integer,Integer> flawSeverityMap = new HashMap<Integer, Integer>();
//    private static SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
//    def static SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
//    private static String BUG_CLOUD_API = "https://src.360.net/api/frontend/thirdapi/apiforcnvd"
//    private static String KEY = "asdoAS9pdohnsdUASHODH89asd8HAShdHasdhAshdluahsduHs"
//    private static String ID = "1001"
//    static {
//        flawTypeMap.put(1,3) //xss
//        flawTypeMap.put(5,18) //配置错误归并为其他
//        flawTypeMap.put(10,5) //弱口令
//        flawTypeMap.put(15,18) //入侵事件归并为其他
//        flawTypeMap.put(20,18) //疑似被黑归并为其他
//        flawTypeMap.put(25,6) //文件上传
//        flawTypeMap.put(30,7) //信息泄露
//        flawTypeMap.put(35,18) //存在后门归并为其他
//        flawTypeMap.put(40,9) //逻辑漏洞
//        flawTypeMap.put(45,20) //代码执行
//        flawTypeMap.put(50,11) //命令执行
//        flawTypeMap.put(55,1) //sql注入
//        flawTypeMap.put(60,18) //解析漏洞归并为其他
//        flawTypeMap.put(65,18) //url跳转归并为其他
//        flawTypeMap.put(70,18) //权限失控归并为其他
//        flawTypeMap.put(75,18) //权限提升归并为其他
//        flawTypeMap.put(80,12) //目录遍历
//        flawTypeMap.put(85,15) //拒绝服务
//        flawTypeMap.put(90,18) //硬编码漏洞归并为其他
//        flawTypeMap.put(95,18) //内存破坏归并为其他
//        flawTypeMap.put(100,18) //溢出漏洞归并为其他
//        flawTypeMap.put(105,18) //虚拟逃逸归并为其他
//        flawTypeMap.put(99,18) //其他
//
//        flawSeverityMap.put(0,20) //低
//        flawSeverityMap.put(1,19) //中
//        flawSeverityMap.put(2,18) //高
//        flawSeverityMap.put(3,18) //严重归并为高
//    }
//    def execute(){
//        return ;
//        Integer page=0;//页码
//        String pagenum="1000";//每页显示数量
//        boolean flag=true;
//        def userIdStr = "19338"
//        Integer userId = Integer.valueOf(userIdStr)
//        String num = "360-" + System.currentTimeMillis();  //生成批次号
//        Calendar cStar = Calendar.getInstance();
//        cStar.add(Calendar.DAY_OF_MONTH, -1);
//        String startTimeStr = sf.format(cStar.getTime())
//        int startTime = (int)(sf.parse(startTimeStr).getTime() / 1000L);
//        println "开始时间=" + startTimeStr
//
//        //截止时间（UNIX时间戳格式），北京时间  eg:2017-01-02
//        Calendar c = Calendar.getInstance();
//        String endTimeStr = sf.format(c.getTime())
//        println "结束时间=" + endTimeStr
//        int endTime = (int)(sf.parse(endTimeStr).getTime() / 1000L);
//        String vulid = "" //默认为空
//        //参数
//        Map params = new HashMap();
//        params.put("id",ID);
//        params.put("start_time",startTime + "");
//        params.put("end_time",endTime + "");
//        params.put("vulid","");
//
//        while(flag){
//            page++;
//            int timeStamp = (int)(new Date().getTime()/1000L)
//            String token = getToken(startTime,endTime,vulid,timeStamp) //token
//            params.put("page",String.valueOf(page));
//            params.put("page_num",pagenum);
//            params.put("timestamp",timeStamp + "");
//            params.put("token",token);
//            //调用接口，获取返回值
//            String content;
//            try{
//                content = HttpClientutils.doPostSSLToJson(BUG_CLOUD_API,new com.alibaba.fastjson.JSONObject(params))
//            }catch(Exception e){
//                e.printStackTrace()
//                SyncAll syncAll = new SyncAll()
//                syncAll.status = "error,获取360漏洞接口数据出错!|startDate="+startTimeStr
//                syncAll.dateCreated = new Date()
//                syncAll.num = num
//                syncAll.save(flush: true)
//                flag=false;
//                return;
//            }
//            //判断返回值是否有内容
//            if(content == null || content.isEmpty()){
//                SyncAll syncAll = new SyncAll()
//                syncAll.status = "error,没有返回内容,page="+page+"|startDate="+startTimeStr
//                syncAll.dateCreated = new Date()
//                syncAll.num = num
//                syncAll.save(flush: true)
//                flag=false;
//                return;
//            }
//            //日志
//            new Receiver().log(content,page)
//            //判断接口状态
//            JSONObject result = JSONObject.fromObject(content)
//            String code = result.getString("code")
//            if("0".equals(code)){
//                //获取漏洞信息
//                String contentArr = result.getString("data")
//                //请求成功，数据为空
//                if(contentArr.equals("null") || contentArr.isEmpty()){
//                    if(page == 1){//如果第1页数据那么记录在表中。
//                        SyncAll syncAll = new SyncAll()
//                        syncAll.status = "warning,接口访问成功，但数据为空，请联系接口提供人!|startDate="+startTimeStr
//                        syncAll.dateCreated = new Date()
//                        syncAll.num = num
//                        syncAll.save(flush: true)
//                    }
//                    flag=false;
//                    return;
//                }
//                JSONArray jsonArray;
//                try{
//                    jsonArray = JSONArray.fromObject(contentArr)
//                }catch(Exception e){
//                    e.printStackTrace()
//                    SyncAll syncAll = new SyncAll()
//                    syncAll.status = "warning,接口访问成功，但数据有误，请联系接口提供人!|startDate="+startTimeStr
//                    syncAll.dateCreated = new Date()
//                    syncAll.num = num
//                    syncAll.save(flush: true)
//                    flag=false;
//                    return;
//                }
//                Object[] objs = jsonArray.toArray()
//                //通过接口成功获取数据
//                if(objs.size() > 0){
//                    SyncAll syncAll = new SyncAll()
//                    syncAll.status = "success，page="+page+"|size="+objs.size()+"|startDate="+startTimeStr
//                    syncAll.dateCreated = new Date()
//                    syncAll.num = num
//                    syncAll.save(flush: true)
//                    //360用户
//                    TUser user = TUser.get(userId)
//                    def filePath = "${grailsApplication.config.filePath.flawAttFilePath}"
//                    // 如果目录不存在，则创建
//                    File flawPathFile = new File(filePath);
//                    if (!flawPathFile.exists()) {
//                        flawPathFile.mkdirs()
//                    }
//                    for(JSONObject bug : objs){
//                        //单条入库日志
//                        SyncOne syncOne = new SyncOne()
//                        syncOne.num = num
//                        syncOne.dateCreated = new Date()
//                        String vid;
//                        try{
//                            vid = bug.getString("vulid")
//                        }catch(Exception e){
//                            e.printStackTrace()
//                            syncOne.qihooId = vid
//                            syncOne.statuOne = "error,奇虎漏洞数据有误，字段无法对应，请联系管理员"
//                            syncOne.save(flush: true)
//                            continue;
//                        }
//                        //只有含有vulid的数据才是正确的数据
//                        try{
//                            if(vid != null && !vid.isEmpty()){
//                                String title = bug.getString("title") //漏洞标题
//                                String createTime = bug.getString("create_time") //创建日期
//                                String type = bug.getString("type") //漏洞类型
//                                String severityId = bug.getString("level_360") //漏洞严重程度
//                                String manufacturerName = bug.getString("company_name") //厂商名称
//                                List<Manufacturer> manufacturerList = Manufacturer.findAllByName(manufacturerName);//根据厂商名称查询厂商
//                                Date openTime = null
//                                if(createTime == null){
//                                    openTime = new Date()
//                                }else{
//                                    openTime = formatter.parse(createTime)
//                                }
//                                def qihooApiDataInstance = QihooApi.findByQihooId(vid)
//                                if(qihooApiDataInstance){
//                                    qihooApiDataInstance.status = 0
//                                    qihooApiDataInstance.save(flush: true)
//                                    syncOne.qihooId = vid
//                                    syncOne.statuOne = "warning,数据库含有该漏洞,故不录入,360漏洞编号为" + vid
//                                    syncOne.foundtime = openTime
//                                    syncOne.save(flush: true)
//                                    continue;
//                                }else {
//                                    qihooApiDataInstance = new QihooApi()
//                                    qihooApiDataInstance.qihooId = vid
//                                }
//                                //根据用户id以及漏洞标题查询漏洞
//                                def flawInstance = Flaw.find("from Flaw t where t.user=:user and t.title=:title and t.enable=1", [user: user, title: title])
//                                if(flawInstance){
//                                    qihooApiDataInstance.status = 0
//                                    qihooApiDataInstance.msg = '漏洞标题重复'
//                                    qihooApiDataInstance.save(flush: true)
//                                    syncOne.qihooId = vid
//                                    syncOne.foundtime = openTime
//                                    syncOne.statuOne = "warning,数据库含有该漏洞,故不录入,360漏洞编号为" + vid
//                                    syncOne.save(flush: true)
//                                    continue;
//                                }
//                                flawInstance = new Flaw()
//                                String url = bug.getString("url") //漏洞url
//                                def util = new Util()
//                                String description = bug.getString("description") //漏洞详情
//                                //如果漏洞详情为空，则不录入该漏洞
//                                if(description == null || description.equals("")){
//                                    qihooApiDataInstance.status = 0
//                                    qihooApiDataInstance.msg = '漏洞描述为空'
//                                    qihooApiDataInstance.save(flush: true)
//                                    continue;
//                                }else {
//                                    flawInstance.title = title //设置漏洞标题
//                                    String detail = bug.getString("detail")
//                                    detail = UnicodeUtils.decodeUnicode(detail)
//                                    detail = detail.replaceAll("<(/*)p>","")
//                                    detail = detail.replaceAll("<br>","\n")
//                                    def attachmentPath = util.saveBugCloudAttachment(detail)
//                                    if(attachmentPath != null){
//                                        File file = new File(attachmentPath)
//                                        def fileName = file.getName()
//                                        def toFilePath = filePath + fileName
//                                        File toFile = new File(toFilePath)
//                                        FileCopyUtils.copy(file, toFile);
//                                        def attachment = new Attachment()
//                                        attachment.realName = fileName
//                                        attachment.fileName = fileName
//                                        attachment.fileType = "application/msword"
//                                        attachment.fileSize = getFileSize(toFile)
//                                        attachment.path = toFilePath
//                                        attachment.save(flush: true)
//                                        flawInstance.attachment = attachment
//                                    }
//                                    def patternStr = "<img\\ssrc=\"(.+?)\">"
//                                    def excludeTag = detail.replace(patternStr,"")
//                                    def detailInfo = new DetailedInfo()
//                                    //cnvd里的漏洞描述 = 360漏洞描述 + 360漏洞详情
//                                    detailInfo.description = description + excludeTag
//                                    flawInstance.detailedInfo = detailInfo //设置漏洞详情
//                                    flawInstance.flawTypesId = flawTypeMap.get(Integer.valueOf(type)) //设置漏洞类型
//                                    flawInstance.serverityId = flawSeverityMap.get(Integer.valueOf(severityId)) //设置漏洞严重程度
//                                    def manufacturer;
//                                    if(manufacturerList != null && !manufacturerList.isEmpty()){
//                                        manufacturer = manufacturerList.get(0)
//                                    }else{
//                                        manufacturer = new Manufacturer()
//                                        manufacturer.name = manufacturerName
//                                        manufacturer.save(flush: true)
//                                    }
//                                    flawInstance.setManufacturer(manufacturer) //设置漏洞厂商
//                                    def date = new Date()
//                                    flawInstance.user = user //设置前台上报用户
//                                    flawInstance.foundTime = openTime //发现时间为白帽子提交到漏洞云的时间
//                                    flawInstance.submitTime = date //设置提交时间
//                                    flawInstance.openTime = openTime //设置公开时间
//                                    flawInstance.dateCreated = openTime //设置漏洞创建时间
//                                    flawInstance.isAttShow = 0 //设置漏洞附件不显示
//                                    flawInstance.isOpen = 0 //默认不公开
//                                    flawInstance.isZero = 0 //默认不是零日漏洞
//                                    flawInstance.isOriginal = 0 //默认原创
//                                    flawInstance.isEvent = 1 //默认时间型漏洞
//                                    flawInstance.status = 2 //设置漏洞状态为二级审核
//                                    flawInstance.firstTime = date //设置一审时间
//                                    flawInstance.secondTime = date //设置二审时间
//                                    if(!flawInstance.save(flush: true)){
//                                        flawInstance.errors.allErrors.each { println it }
//                                    }else {
//                                        flawInstance.parentId = flawInstance.id
//                                        flawInstance.save(flush: true)
//                                        FlawUrl flawUrl = new FlawUrl()
//                                        flawUrl.flaw = flawInstance
//                                        flawUrl.url = url
//                                        if (!flawUrl.save(flush: true)) {
//                                            flawUrl.errors.allErrors.each { println it }
//                                        }
//                                    }
//                                    qihooApiDataInstance.status = 1
//                                    qihooApiDataInstance.flaw = flawInstance
//                                    if (!qihooApiDataInstance.save(flush: true)) {
//                                        syncOne.qihooId = vid
//                                        syncOne.statuOne = "保存失败"
//                                        syncOne.foundtime = openTime
//                                        syncOne.save(flush: true)
//                                        qihooApiDataInstance.errors.allErrors.each { println it }
//                                        continue;
//                                    }
//                                    //保存单条漏洞录入日志
//                                    syncOne.qihooId = vid
//                                    syncOne.statuOne = "success"
//                                    syncOne.foundtime = openTime
//                                    syncOne.save(flush: true)
//                                }
//                            }else{
//                                syncOne.qihooId = ""
//                                syncOne.statuOne = "error,360漏洞数据有误，该批次的数据不符合录入规则"
//                                syncOne.save(flush: true)
//                                continue;
//                            }
//                        }catch(Exception e){
//                            e.printStackTrace()
//                            syncOne.qihooId = vid
//                            syncOne.statuOne = "error,漏洞入库出现错误,奇虎漏洞编号为为" + vid
//                            syncOne.save(flush: true)
//                            continue;
//                        }
//                    }
//                }else{
//                    //漏洞获取接口访问成功
//                    SyncAll qihooalls = new SyncAll()
//                    qihooalls.dateCreated = new Date()
//                    qihooalls.num = num
//                    qihooalls.status = "warning,接口访问成功，但数据有误，请联系接口提供人!|startDate="+startTimeStr
//                    qihooalls.save(flush: true)
//                    flag=false;
//                }
//            }else{
//                SyncAll qihooalls = new SyncAll()
//                qihooalls.dateCreated = new Date()
//                qihooalls.num = num
//                qihooalls.status = "error," + result.getString("message")+"|startDate="+startTimeStr
//                qihooalls.save(flush: true)
//                switch (code) {
//                    case "1003": println "请求参数错误" + ", 请求时间" + formatter.format(new Date()); break;
//                    case "1004": println "漏洞编码错误" + ", 请求时间" + formatter.format(new Date()); break;
//                    case "1005": println "签名错误" + ", 请求时间" + formatter.format(new Date()); break;
//                    case "1010": println "时间错误" + ", 请求时间" + formatter.format(new Date()); break;
//                    case "1011": println "时间范围参数错误" + ", 请求时间" + formatter.format(new Date()); break;
//                    case "1017": println "IP不在白名单内" + ", 请求时间" + formatter.format(new Date()); break;
//                    default: break;
//                }
//                flag=false;
//            }
//        }
//    }
//
//    //获取token
//    def getToken(int startTime,int endTime,String vulid,int timeStamp){
//        StringBuffer tokenStr = new StringBuffer();
//        tokenStr.append(KEY);
//        if(startTime != 0){
//            tokenStr.append(startTime);
//        }
//        if(endTime != 0){
//            tokenStr.append(endTime);
//        }
//        if(org.apache.commons.lang.StringUtils.isNotBlank(vulid)){
//            tokenStr.append(vulid);
//        }
//        tokenStr.append(timeStamp).append(ID);
//        System.out.println(tokenStr.toString());
//        String token = MD5.getMD5Str(tokenStr.toString()).toUpperCase();
//        return token;
//    }
//
//    def long getFileSize(File file) {
//        long s = 0;
//        if (file.exists()) {
//            FileInputStream fis = null;
//            fis = new FileInputStream(file);
//            s = fis.available();
//        }
//        return s;
//    }
//
//}
