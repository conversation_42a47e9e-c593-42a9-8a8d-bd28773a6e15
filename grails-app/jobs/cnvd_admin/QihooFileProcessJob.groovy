package cnvd_admin

import com.cnvd.TUser
import com.cnvd.api.QihooApi
import com.cnvd.common.Attachment
import com.cnvd.flawInfo.DetailedInfo
import com.cnvd.flawInfo.Flaw
import com.cnvd.flawInfo.FlawUrl
import com.cnvd.listener.QihooAll
import com.cnvd.listener.QihooOne
import com.cnvd.wooyun.util.Util
import com.cnvd.wooyun.util.WordUtil
import net.sf.json.JSONArray
import net.sf.json.JSONObject
import org.codehaus.groovy.grails.commons.ConfigurationHolder
import org.springframework.util.FileCopyUtils

import java.text.SimpleDateFormat
import java.util.concurrent.locks.ReentrantLock
import java.util.regex.Matcher
import java.util.regex.Pattern

/**
 * 奇虎文件处理定时任务
 * 每分钟扫描 QIHOO_MIDDLE_PATH 和 QIHOO_LOG_PATH 目录
 * 读取文件进行数据处理，处理完将文件移动到 QIHOO_FINISH_PATH
 */
class QihooFileProcessJob {
    
    // 每分钟执行一次
    static triggers = {
        cron name: 'qihooFileProcessTrigger', cronExpression: '0 * * * * ?'
    }

    def attachmentService
    def flawService
    def grailsApplication
    static SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")

    def static config = ConfigurationHolder.config
    static SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
    
    private static final SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
    private static final ReentrantLock lock = new ReentrantLock()
    
    def execute() {
        // 使用锁防止并发执行
        if (!lock.tryLock()) {
            println "[${formatter.format(new Date())}] QihooFileProcessJob 已在执行中，跳过本次执行"
            return
        }
        
        try {
            println "[${formatter.format(new Date())}] QihooFileProcessJob 开始执行"
            
            // 处理 QIHOO_LOG_PATH 目录的文件
            processLogDirectory(Util.QIHOO_LOG_PATH, "LOG")
            
            // 处理 QIHOO_MIDDLE_PATH 目录的文件  
            processDirectory(Util.QIHOO_MIDDLE_PATH, "MIDDLE")
            
            println "[${formatter.format(new Date())}] QihooFileProcessJob 执行完成"
            
        } catch (Exception e) {
            println "[${formatter.format(new Date())}] QihooFileProcessJob 执行出错: ${e.message}"
            e.printStackTrace()
        } finally {
            lock.unlock()
        }
    }
    
    /**
     * 处理指定目录下的文件
     * @param dirPath 目录路径
     * @param dirType 目录类型（用于日志）
     */
    private void processDirectory(String dirPath, String dirType) {
        File dir = new File(dirPath)
        if (!dir.exists()) {
            dir.mkdirs();
        }
        
        if (!dir.isDirectory()) {
            println "[${formatter.format(new Date())}] 路径不是目录: ${dirPath}"
            return
        }
        
        File[] files = dir.listFiles(new FileFilter() {
            boolean accept(File file) {
                return file.isFile() && !file.getName().startsWith(".")
            }
        })
        
        if (files == null || files.length == 0) {
            println "[${formatter.format(new Date())}] ${dirType} 目录为空: ${dirPath}"
            return
        }
        
        println "[${formatter.format(new Date())}] ${dirType} 目录找到 ${files.length} 个文件"
        
        for (File file : files) {
            try {
                processFile(file, dirType)
            } catch (Exception e) {
                println "[${formatter.format(new Date())}] 处理文件失败: ${file.absolutePath}, 错误: ${e.message}"
                e.printStackTrace()
            }
        }
    }

    private void processLogDirectory(String dirPath, String dirType) {
        File dir = new File(dirPath)
        if (!dir.exists()) {
            dir.mkdirs();
        }

        if (!dir.isDirectory()) {
            println "[${formatter.format(new Date())}] 路径不是目录: ${dirPath}"
            return
        }

        File[] files = dir.listFiles(new FileFilter() {
            boolean accept(File file) {
                return file.isFile() && !file.getName().startsWith(".")
            }
        })

        if (files == null || files.length == 0) {
            println "[${formatter.format(new Date())}] ${dirType} 目录为空: ${dirPath}"
            return
        }

        println "[${formatter.format(new Date())}] ${dirType} 目录找到 ${files.length} 个文件"

        for (File file : files) {
            try {
                moveToMiddleDirectory(file)
            } catch (Exception e) {
                println "[${formatter.format(new Date())}] 处理文件失败: ${file.absolutePath}, 错误: ${e.message}"
                e.printStackTrace()
            }
        }
    }

    /**
     * 处理单个文件
     * @param file 要处理的文件
     * @param dirType 目录类型
     */
    private void processFile(File file, String dirType) {
        println "[${formatter.format(new Date())}] 开始处理文件: ${file.absolutePath}"
        
        // 读取文件内容
        String content = readFileContent(file)
        if (content == null || content.trim().isEmpty()) {
            println "[${formatter.format(new Date())}] 文件内容为空，跳过处理: ${file.absolutePath}"
            return
        }
        
        // 处理文件内容
        boolean processResult = processFileContent(content, file.getName(), dirType)
        
        if (processResult) {
            // 处理成功，移动文件到完成目录
            moveFileToFinishDirectory(file)
        } else {
            println "[${formatter.format(new Date())}] 文件处理失败，保留原文件: ${file.absolutePath}"
        }
    }
    
    /**
     * 读取文件内容
     * @param file 文件对象
     * @return 文件内容字符串
     */
    private String readFileContent(File file) {
        BufferedReader reader = null
        StringBuilder content = new StringBuilder()
        
        try {
            reader = new BufferedReader(new InputStreamReader(new FileInputStream(file), "UTF-8"))
            String line
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n")
            }
            return content.toString()
        } catch (IOException e) {
            println "[${formatter.format(new Date())}] 读取文件失败: ${file.absolutePath}, 错误: ${e.message}"
            return null
        } finally {
            if (reader != null) {
                try {
                    reader.close()
                } catch (IOException e) {
                    // 忽略关闭异常
                }
            }
        }
    }
    
    /**
     * 处理文件内容
     * @param content 文件内容
     * @param fileName 文件名
     * @param dirType 目录类型
     * @return 处理是否成功
     */
    private boolean processFileContent(String content, String fileName, String dirType) {
        try {
            println "[${formatter.format(new Date())}] 处理文件内容: ${fileName}, 内容长度: ${content.length()}"
            
            //处理数据
            process(content)
            
            println "[${formatter.format(new Date())}] 文件内容处理成功: ${fileName}"
            return true
            
        } catch (Exception e) {
            println "[${formatter.format(new Date())}] 处理文件内容失败: ${fileName}, 错误: ${e.message}"
            e.printStackTrace()
            return false
        }
    }
    
    /**
     * 将文件移动到完成目录
     * @param file 要移动的文件
     */
    private void moveFileToFinishDirectory(File file) {
        try {
            // 确保完成目录存在
            File finishDir = new File(Util.QIHOO_FINISH_PATH)
            if (!finishDir.exists()) {
                finishDir.mkdirs()
            }
            
            // 生成目标文件路径
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date())
            String targetFileName = timestamp + "_" + file.getName()
            File targetFile = new File(finishDir, targetFileName)
            
            // 移动文件
            if (file.renameTo(targetFile)) {
                println "[${formatter.format(new Date())}] 文件移动成功: ${file.absolutePath} -> ${targetFile.absolutePath}"
            } else {
                // 如果重命名失败，尝试复制后删除
                copyFile(file, targetFile)
                if (file.delete()) {
                    println "[${formatter.format(new Date())}] 文件复制并删除成功: ${file.absolutePath} -> ${targetFile.absolutePath}"
                } else {
                    println "[${formatter.format(new Date())}] 文件复制成功但删除原文件失败: ${file.absolutePath}"
                }
            }
            
        } catch (Exception e) {
            println "[${formatter.format(new Date())}] 移动文件失败: ${file.absolutePath}, 错误: ${e.message}"
            e.printStackTrace()
        }
    }

    private void moveToMiddleDirectory(File file) {
        try {
            // 确保完成目录存在
            File middleDir = new File(Util.QIHOO_MIDDLE_PATH)
            if (!middleDir.exists()) {
                middleDir.mkdirs()
            }

            // 生成目标文件路径
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date())
            String targetFileName = timestamp + "_" + file.getName()
            File targetFile = new File(middleDir, targetFileName)

            // 移动文件
            if (file.renameTo(targetFile)) {
                println "[${formatter.format(new Date())}] 文件移动成功: ${file.absolutePath} -> ${targetFile.absolutePath}"
            } else {
                // 如果重命名失败，尝试复制后删除
                copyFile(file, targetFile)
                if (file.delete()) {
                    println "[${formatter.format(new Date())}] 文件复制并删除成功: ${file.absolutePath} -> ${targetFile.absolutePath}"
                } else {
                    println "[${formatter.format(new Date())}] 文件复制成功但删除原文件失败: ${file.absolutePath}"
                }
            }

        } catch (Exception e) {
            println "[${formatter.format(new Date())}] 移动文件失败: ${file.absolutePath}, 错误: ${e.message}"
            e.printStackTrace()
        }
    }

    /**
     * 复制文件（Java 1.7 兼容版本）
     * @param source 源文件
     * @param target 目标文件
     */
    private void copyFile(File source, File target) throws IOException {
        FileInputStream fis = null
        FileOutputStream fos = null
        
        try {
            fis = new FileInputStream(source)
            fos = new FileOutputStream(target)
            
            byte[] buffer = new byte[8192]
            int bytesRead
            while ((bytesRead = fis.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead)
            }
            fos.flush()
            
        } finally {
            if (fis != null) {
                try {
                    fis.close()
                } catch (IOException e) {
                    // 忽略关闭异常
                }
            }
            if (fos != null) {
                try {
                    fos.close()
                } catch (IOException e) {
                    // 忽略关闭异常
                }
            }
        }
    }

    long getFileSize(File file) {
        long s = 0;
        if (file.exists()) {
            FileInputStream fis = null;
            fis = new FileInputStream(file);
            s = fis.available();
        }
        return s;
    }

    private void process(String content) {
        JSONObject json = JSONObject.fromObject(content);
        String code = json.getString("code");
        String t1 = "qihoo" + System.currentTimeMillis();
        Calendar cStar = Calendar.getInstance();
        cStar.add(Calendar.DAY_OF_MONTH, -1);
        String startTimeStr = sf.format(cStar.getTime())

        if (code.equals("0000")) {
            //格式化漏洞内容
            String contArr = json.getString("data");
            //\u8bf7\u6c42\u6210\u529f,\u6570\u636e\u4e3a\u7a7a  请求成功,数据为空
            if (contArr.equals("")) {
                //获取漏洞信息接口出错
                QihooAll qihooalls = new QihooAll()
                qihooalls.dateCreated = new Date()
                qihooalls.num = t1
                qihooalls.status = "worning,接口访问成功，但数据为空，请联系接口提供人!"
                qihooalls.save(flush: true)
                return;
            }

            JSONArray jsonArray;
            try {
                jsonArray = JSONArray.fromObject(contArr);
//				//漏洞获取接口访问成功
//				QihooAll qihooalls = new QihooAll()
//				qihooalls.dateCreated = new Date()
//				qihooalls.num = t1
//				qihooalls.status = "success"
//				qihooalls.save(flush:true)
            } catch (Exception e) {
                e.printStackTrace()
                //获取漏洞信息接口出错
                QihooAll qihooalls = new QihooAll()
                qihooalls.dateCreated = new Date()
                qihooalls.num = t1
                qihooalls.status = "worning,接口访问成功，但数据有误，请联系接口提供人!"
                qihooalls.save(flush: true)
                return;
            }
            Object[] strs = jsonArray.toArray();
            if (strs.size() > 0) {
                //漏洞获取接口访问成功
                QihooAll qihooalls = new QihooAll()
                qihooalls.dateCreated = new Date()
                qihooalls.num = t1
                qihooalls.status = "success,size="+strs.length+",startTimeStr="+startTimeStr
                qihooalls.save(flush: true)
                //360用户
                //TUser user = TUser.findByEmail("<EMAIL>")
                // 2018-04-26 17:24:30 更改成了  <EMAIL>
                TUser user = TUser.get(9395)
                def filePath = "${grailsApplication.config.filePath.flawAttFilePath}"
                // 如果目录不存在，则创建
                File flawPathFile = new File(filePath);
                if (!flawPathFile.exists()) {
                    flawPathFile.mkdirs()
                }
                int index=1;
                int size=strs.size();
                System.out.println("共计 "+size+" 条数据")
                for (JSONObject s : strs) {
                    System.out.println("开始处理第"+index+"条数据===="+s.toString());
                    long startTime = System.currentTimeMillis();//获取开始时间
                    //奇虎编号
                    String vId;
                    //单条入库日志
                    QihooOne qihooone = new QihooOne()
                    qihooone.num = t1
                    qihooone.dateCreated = new Date()
                    try {
                        vId = s.getString("vulid");
                    } catch (Exception e) {
                        e.printStackTrace()
                        qihooone.qihooId = vId
                        qihooone.statuOne = "error,奇虎漏洞数据有误，字段无法对应，请联系管理员"
                        qihooone.save(flush: true)
                        index++;
                        continue
                    }
                    try {
                        //判断只有含有漏洞qihooId的才是正常数据
                        if (vId != null && !"".equals(vId)) {
                            //漏洞标题
                            String title = s.getString("title");
                            Date openTime = null;
                            if (s.getString("create_time") == null) {
                                openTime = new Date()
                            } else {
                                openTime = formatter.parse(s.getString("create_time"))
                            }

                            //对数据进行处理
//							def qihooApiDataInstance = new QihooApi()
                            def qihooApiDataInstance = QihooApi.findByQihooId(vId)
                            if (qihooApiDataInstance) {
                                qihooApiDataInstance.status = 0
                                qihooApiDataInstance.save(flush: true)
                                qihooone.qihooId = vId
                                qihooone.statuOne = "warn,数据库含有该漏洞,故不录入,奇虎漏洞编号为" + vId
                                qihooone.foundtime = openTime
                                qihooone.save(flush: true)
                                index++;
                                continue
                            } else {
                                qihooApiDataInstance = new QihooApi()
                                qihooApiDataInstance.qihooId = vId
                            }


//                            def flawInstance = Flaw.find("from Flaw t where t.user=:user and t.title=:title and t.enable=1", [user: user, title: title])
//                            if (flawInstance) {
//                                qihooApiDataInstance.status = 0
//                                qihooApiDataInstance.msg = '漏洞标题重复'
//                                qihooApiDataInstance.save(flush: true)
//                                qihooone.qihooId = vId
//                                qihooone.foundtime = openTime
//                                qihooone.statuOne = "warn,数据库含有该漏洞,故不录入,奇虎漏洞编号为" + vId
//                                qihooone.save(flush: true)
//                                index++;
//                                continue
//                            }
                            def flawInstance = new Flaw()
                            def wordUtil = new WordUtil()
                            //漏洞描述
                            String description = s.getString("description")
                            //按照老版本接口规定，如果漏洞描述为空，则不录入该漏洞
                            if (description == null || description.equals("")) {
                                qihooApiDataInstance.status = 0
                                qihooApiDataInstance.msg = '漏洞描述为空'
                                qihooApiDataInstance.save(flush: true)
                                index++;
                                continue;
                            } else {
                                //如果描述不为空  ，保存
                                //漏洞详情，里面含有可下载的图片等附件
                                String detail = s.getString("detail");
                                def patternStr = "<img\\ssrc=\"(.+?)\">";
                                def d = detail.replaceAll(patternStr, "")
                                def attachmentPath = wordUtil.saveQihooAttachment(detail)
                                if (attachmentPath != null) {
                                    File file = new File(attachmentPath)
                                    def fileName = file.getName()
                                    def toFilePath = filePath + fileName
                                    File toFile = new File(toFilePath)
                                    FileCopyUtils.copy(file, toFile);
                                    def attachment = new Attachment()
                                    attachment.realName = fileName
                                    attachment.fileName = fileName
                                    attachment.fileType = "application/msword"
                                    attachment.fileSize = getFileSize(toFile)
                                    attachment.path = toFilePath
                                    attachment.save(flush: true)
                                    flawInstance.attachment = attachment
                                }
                                flawInstance.title = title
                                def detailInfo = new DetailedInfo()
                                //cnvd库中的漏洞描述=360漏洞描述+360漏洞详情
                                detailInfo.description = description + d
                                flawInstance.detailedInfo = detailInfo
                                /*Date openTime = null;
                                if (s.getString("create_time") == null) {
                                    openTime = new Date()
                                } else {
                                    openTime = formatter.parse(s.getString("create_time"))
                                }*/
                                def date = new Date()
                                flawInstance.user = user
                                flawInstance.foundTime = openTime
                                flawInstance.submitTime = date
                                flawInstance.openTime = openTime
                                flawInstance.isAttShow = 0
                                flawInstance.isOpen = 0
                                flawInstance.isZero = 0
                                flawInstance.isOriginal = 0
                                flawInstance.isEvent = 1
                                //备注：新版接口中，是否为事件型初始为事件型、参考链接  两个字段  放入二级审核
                                flawInstance.status = 2
                                // 第二次审核的时间
                                flawInstance.firstTime = new Date()
                                flawInstance.secondTime = new Date()
                                if (!flawInstance.save(flush: true)) {
                                    flawInstance.errors.allErrors.each { println it }
                                } else {
                                    //增加分组父节点--20191104
                                    flawInstance.parentId=flawInstance.id
                                    //提取url
                                    String text = d + "," + detail
                                    //提取url
                                    // url正则
                                    String regex2 = "((https|http|ftp|rtsp|mms)?:\\/\\/([\\w-]+\\.)+[\\w-]+([\\w-./:?%&*=]*))";
                                    Matcher m7 = Pattern.compile(regex2, Pattern.CASE_INSENSITIVE).matcher(text);
                                    while (m7.find()) {
                                        FlawUrl fu = FlawUrl.findByUrlAndFlaw(m7.group().trim(), flawInstance)
                                        if (!fu) {
                                            FlawUrl flawUrl = new FlawUrl()
                                            flawUrl.flaw = flawInstance
                                            flawUrl.url = m7.group().trim()
                                            if (!flawUrl.save(flush: true)) {
                                                flawUrl.errors.allErrors.each { println it }
                                            }
                                            //保存漏洞url到redis
                                            flawService.setFlawUrl(flawInstance.getId(),m7.group().trim())
                                        }
                                    }
                                    System.out.println("入库成功|漏洞id="+flawInstance.id+"|size="+size+"|index="+index+"|漏洞入库时间="+(System.currentTimeMillis()-startTime));
                                    index++;
                                }
                                qihooApiDataInstance.status = 1
                                qihooApiDataInstance.flaw = flawInstance
                                if (!qihooApiDataInstance.save(flush: true)) {
                                    qihooone.qihooId = vId
                                    qihooone.statuOne = "保存失败"
                                    qihooone.foundtime = openTime
                                    qihooone.save(flush: true)
                                    qihooApiDataInstance.errors.allErrors.each { println it }
                                    index++;
                                    continue
                                }
                                //保存单条漏洞录入日志
                                qihooone.qihooId = vId
                                qihooone.statuOne = "success"
                                qihooone.foundtime = openTime
                                qihooone.save(flush: true)
                            }
                        } else {
                            qihooone.qihooId = ""
                            qihooone.statuOne = "error,奇虎漏洞数据有误，该批次的数据不符合录入规则"
                            qihooone.save(flush: true)
                            index++;
                            continue
                        }
                    } catch (Exception e) {
                        e.printStackTrace()
                        qihooone.qihooId = vId
                        qihooone.statuOne = "error,漏洞入库出现错误,奇虎漏洞编号为为" + vId
                        qihooone.save(flush: true)
                        index++;
                        continue
                    }
                }
            } else {
                //漏洞获取接口访问成功
                QihooAll qihooalls = new QihooAll()
                qihooalls.dateCreated = new Date()
                qihooalls.num = t1
                qihooalls.status = "worning,接口访问成功，但数据有误，请联系接口提供人!"
                qihooalls.save(flush: true)
            }
        } else {
            QihooAll qihooalls = new QihooAll()
            qihooalls.dateCreated = new Date()
            qihooalls.num = t1
            qihooalls.status = "error," + json.getString("msg")
            qihooalls.save(flush: true)
            switch (code) {
                case "1001": println "非法请求" + ",  请求时间" + formatter.format(new Date()); break;
                case "1002": println "请求超时" + ",  请求时间" + formatter.format(new Date()); break;
                case "1003": println "请求参数错误" + ",  请求时间" + formatter.format(new Date()); break;
                case "1004": println "漏洞编码错误" + ",  请求时间" + formatter.format(new Date()); break;
                case "1005": println "签名错误" + ",  请求时间" + formatter.format(new Date()); break;
                case "1006": println "无权限" + ",  请求时间" + formatter.format(new Date()); break;
                case "1007": println "漏洞不存在" + ",  请求时间" + formatter.format(new Date()); break;
                default: break;
            }
        }

    }
}
