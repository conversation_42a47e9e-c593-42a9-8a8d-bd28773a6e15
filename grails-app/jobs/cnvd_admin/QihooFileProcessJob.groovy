package cnvd_admin

import com.cnvd.wooyun.util.Util
import com.cnvd.qihoo.Receiver
import java.io.*
import java.text.SimpleDateFormat
import java.util.concurrent.locks.ReentrantLock

/**
 * 奇虎文件处理定时任务
 * 每分钟扫描 QIHOO_MIDDLE_PATH 和 QIHOO_LOG_PATH 目录
 * 读取文件进行数据处理，处理完将文件移动到 QIHOO_FINISH_PATH
 */
class QihooFileProcessJob {
    
    // 每分钟执行一次
    static triggers = {
        cron name: 'qihooFileProcessTrigger', cronExpression: '0 * * * * ?'
    }
    
    private static final SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
    private static final ReentrantLock lock = new ReentrantLock()
    
    def execute() {
        // 使用锁防止并发执行
        if (!lock.tryLock()) {
            println "[${formatter.format(new Date())}] QihooFileProcessJob 已在执行中，跳过本次执行"
            return
        }
        
        try {
            println "[${formatter.format(new Date())}] QihooFileProcessJob 开始执行"
            
            // 处理 QIHOO_LOG_PATH 目录的文件
            processDirectory(Util.QIHOO_LOG_PATH, "LOG")
            
            // 处理 QIHOO_MIDDLE_PATH 目录的文件  
            processDirectory(Util.QIHOO_MIDDLE_PATH, "MIDDLE")
            
            println "[${formatter.format(new Date())}] QihooFileProcessJob 执行完成"
            
        } catch (Exception e) {
            println "[${formatter.format(new Date())}] QihooFileProcessJob 执行出错: ${e.message}"
            e.printStackTrace()
        } finally {
            lock.unlock()
        }
    }
    
    /**
     * 处理指定目录下的文件
     * @param dirPath 目录路径
     * @param dirType 目录类型（用于日志）
     */
    private void processDirectory(String dirPath, String dirType) {
        File dir = new File(dirPath)
        if (!dir.exists()) {
            println "[${formatter.format(new Date())}] 目录不存在: ${dirPath}"
            return
        }
        
        if (!dir.isDirectory()) {
            println "[${formatter.format(new Date())}] 路径不是目录: ${dirPath}"
            return
        }
        
        File[] files = dir.listFiles(new FileFilter() {
            public boolean accept(File file) {
                return file.isFile() && !file.getName().startsWith(".")
            }
        })
        
        if (files == null || files.length == 0) {
            println "[${formatter.format(new Date())}] ${dirType} 目录为空: ${dirPath}"
            return
        }
        
        println "[${formatter.format(new Date())}] ${dirType} 目录找到 ${files.length} 个文件"
        
        for (File file : files) {
            try {
                processFile(file, dirType)
            } catch (Exception e) {
                println "[${formatter.format(new Date())}] 处理文件失败: ${file.absolutePath}, 错误: ${e.message}"
                e.printStackTrace()
            }
        }
    }
    
    /**
     * 处理单个文件
     * @param file 要处理的文件
     * @param dirType 目录类型
     */
    private void processFile(File file, String dirType) {
        println "[${formatter.format(new Date())}] 开始处理文件: ${file.absolutePath}"
        
        // 读取文件内容
        String content = readFileContent(file)
        if (content == null || content.trim().isEmpty()) {
            println "[${formatter.format(new Date())}] 文件内容为空，跳过处理: ${file.absolutePath}"
            return
        }
        
        // 处理文件内容
        boolean processResult = processFileContent(content, file.getName(), dirType)
        
        if (processResult) {
            // 处理成功，移动文件到完成目录
            moveFileToFinishDirectory(file)
        } else {
            println "[${formatter.format(new Date())}] 文件处理失败，保留原文件: ${file.absolutePath}"
        }
    }
    
    /**
     * 读取文件内容
     * @param file 文件对象
     * @return 文件内容字符串
     */
    private String readFileContent(File file) {
        BufferedReader reader = null
        StringBuilder content = new StringBuilder()
        
        try {
            reader = new BufferedReader(new InputStreamReader(new FileInputStream(file), "UTF-8"))
            String line
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n")
            }
            return content.toString()
        } catch (IOException e) {
            println "[${formatter.format(new Date())}] 读取文件失败: ${file.absolutePath}, 错误: ${e.message}"
            return null
        } finally {
            if (reader != null) {
                try {
                    reader.close()
                } catch (IOException e) {
                    // 忽略关闭异常
                }
            }
        }
    }
    
    /**
     * 处理文件内容
     * @param content 文件内容
     * @param fileName 文件名
     * @param dirType 目录类型
     * @return 处理是否成功
     */
    private boolean processFileContent(String content, String fileName, String dirType) {
        try {
            println "[${formatter.format(new Date())}] 处理文件内容: ${fileName}, 内容长度: ${content.length()}"
            
            // 调用 Receiver 的 log 方法处理数据
            new Receiver().log(content)
            
            println "[${formatter.format(new Date())}] 文件内容处理成功: ${fileName}"
            return true
            
        } catch (Exception e) {
            println "[${formatter.format(new Date())}] 处理文件内容失败: ${fileName}, 错误: ${e.message}"
            e.printStackTrace()
            return false
        }
    }
    
    /**
     * 将文件移动到完成目录
     * @param file 要移动的文件
     */
    private void moveFileToFinishDirectory(File file) {
        try {
            // 确保完成目录存在
            File finishDir = new File(Util.QIHOO_FINISH_PATH)
            if (!finishDir.exists()) {
                finishDir.mkdirs()
            }
            
            // 生成目标文件路径
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date())
            String targetFileName = timestamp + "_" + file.getName()
            File targetFile = new File(finishDir, targetFileName)
            
            // 移动文件
            if (file.renameTo(targetFile)) {
                println "[${formatter.format(new Date())}] 文件移动成功: ${file.absolutePath} -> ${targetFile.absolutePath}"
            } else {
                // 如果重命名失败，尝试复制后删除
                copyFile(file, targetFile)
                if (file.delete()) {
                    println "[${formatter.format(new Date())}] 文件复制并删除成功: ${file.absolutePath} -> ${targetFile.absolutePath}"
                } else {
                    println "[${formatter.format(new Date())}] 文件复制成功但删除原文件失败: ${file.absolutePath}"
                }
            }
            
        } catch (Exception e) {
            println "[${formatter.format(new Date())}] 移动文件失败: ${file.absolutePath}, 错误: ${e.message}"
            e.printStackTrace()
        }
    }
    
    /**
     * 复制文件（Java 1.7 兼容版本）
     * @param source 源文件
     * @param target 目标文件
     */
    private void copyFile(File source, File target) throws IOException {
        FileInputStream fis = null
        FileOutputStream fos = null
        
        try {
            fis = new FileInputStream(source)
            fos = new FileOutputStream(target)
            
            byte[] buffer = new byte[8192]
            int bytesRead
            while ((bytesRead = fis.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead)
            }
            fos.flush()
            
        } finally {
            if (fis != null) {
                try {
                    fis.close()
                } catch (IOException e) {
                    // 忽略关闭异常
                }
            }
            if (fos != null) {
                try {
                    fos.close()
                } catch (IOException e) {
                    // 忽略关闭异常
                }
            }
        }
    }
}
