package com.cnvd.wooyun.util;

import java.util.UUID;

public class Util {
	public static final String WOO_YUN_IMAGE_PREFIX = "http://www.wooyun.org";
	public static final String WOO_YUN_REFERER = "http://www.wooyun.org/";
	public static final String SAVE_IMAGE_PATH = "/tmp/wooyun/img";
	public static final String SAVE_DOC_PATH = "/tmp/wooyun/docx";
	public static final String LOG_PATH = "/data/wooyun/logs";
	
	public static final String SAVE_IMAGE_PATH_QIHOO = "/home/<USER>/tmp/qihoo/img";
	public static final String SAVE_DOC_PATH_QIHOO = "/home/<USER>/tmp/qihoo/docx";
	public static final String QIHOO_LOG_PATH = "/home/<USER>/data/qihoo/logs";
	public static final String QIHOO_MIDDLE_PATH = "/home/<USER>/data/qihoo/logs_middle";
	public static final String QIHOO_FINISH_PATH = "/home/<USER>/data/qihoo/logs_finish";
	public static final String QIHOO_LOCK = "/home/<USER>/data/qihoo/lock";

	public String uuid() {
		String uuid = UUID.randomUUID().toString().replaceAll("-", "");
		return uuid;
	}
}
