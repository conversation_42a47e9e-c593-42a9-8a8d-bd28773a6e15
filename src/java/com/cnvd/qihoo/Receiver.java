package com.cnvd.qihoo;

import com.cnvd.JsonUtil;
import com.cnvd.MD5;
import com.cnvd.listener.QihooAll;
import com.cnvd.wooyun.util.Util;
import net.sf.json.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.codehaus.groovy.grails.commons.ConfigurationHolder;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class Receiver {


	private static String getaddcert = "http://butian.360.cn/api/getaddcert/";
	private static String getall = "http://butian.360.cn/api/getall/";//他们给我们所有的id
	private static String getadd = "http://butian.360.cn/api/getadd/";//是10分钟的新增
	private static String getone = "http://butian.360.cn/api/getone/";//可以指定获取某一个的
	private static String getSender = "http://butian.360.cn/api/invul";
	private final static String suffix = "_54a3e75bbad41bee4768a896c9e2807578fc9d25";


	private static SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	private static SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
	private static SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
	private static String getTimeStamp = "https://api.butian.net/Interface/getTimeStamp";//获取目标服务器时间戳
	private static String getNewApi = "https://api.butian.net/Interface";//补天获取数据接口
	private final static String key = "EE5065448620E4386EE8F5DDC3F97CB8"; //key值

	public static void main(String[] args) {
		String t1 = "qihoo" + System.currentTimeMillis();  //生成批次号
		//获取目标服务器时间戳
		String requestMethod = "POST";
		String outputStr = null;
		String timestamp;
		try {
			//timestamp = RequestUtil.httpsRequest(getTimeStamp, requestMethod,outputStr);
			timestamp = HttpClientutils.doPostSSLToJson(getTimeStamp, new JSONObject());
			System.out.println("时间戳=" + timestamp);
		} catch (Exception e) {
			e.printStackTrace();
			return;
		}
		//判断是否有返回值
		if (timestamp == null || "".equals(timestamp)) {
			System.out.println("时间戳获取错误");
			return;
		}
		//调用数据获取接口
		String idStr = "1";

		//应补天接口方要求，日期为3天之内的 20191107 我方默认设置成4天内的数据了
		Calendar cStar = Calendar.getInstance();
		cStar.add(Calendar.DAY_OF_MONTH, -2);
		String startTimeStr = sf.format(cStar.getTime());
		startTimeStr="2022-03-14";
		//def starttime="${grailsApplication.config.url.starttime}"
		//println "starttime=" + starttime
		//if(starttime){
		//startTimeStr=starttime//默认取当前时间，如果从新获取之前的数据，更改配置文件的starttime开始时间
		//}
		String start_time = null;
		try {
			start_time = String.valueOf(sf.parse(startTimeStr).getTime() / 1000L);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		System.out.println("开始时间=" + startTimeStr);

		//截止时间（UNIX时间戳格式），北京时间  eg:2017-01-02
		//当前日期加一天
		Calendar c = Calendar.getInstance();
		c.add(Calendar.DAY_OF_MONTH, 1);
		String endTimeStr = sf.format(c.getTime());
		endTimeStr="2022-03-15";
		System.out.println("结束时间=" + endTimeStr);
		String end_time = null;
		try {
			end_time = String.valueOf(sf.parse(endTimeStr).getTime() / 1000L);
		} catch (ParseException e) {
			e.printStackTrace();
		}

		//key,start_time,end_time,vulid,timestamp,id按顺序拼接。(key,timestamp,id为必填项)
		String tokenStr = key + start_time + end_time + timestamp + idStr;
		String token = MD5.getMD5Str(tokenStr).toUpperCase();
		//拼接post请求param
		String param = "id=" + idStr + "&timestamp=" + timestamp + "&token=" + token + "&start_time=" + start_time + "&end_time=" + end_time;
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("id", idStr);
		params.put("timestamp", timestamp);
		params.put("token", token);
		params.put("start_time", start_time);
		params.put("end_time", end_time);
		System.out.println("params:" + params);
		//访问补天获取漏洞详情的接口
		String apiMethod = "POST";
		String content;
		try {
			System.out.println("接口地址："+getNewApi + "?" + param);
			long startTime = System.currentTimeMillis();    //获取开始时间
			content = HttpClientutils.newDoPost(getNewApi, param, "utf-8");
			long endTime = System.currentTimeMillis();    //获取结束时间
			System.out.println("程序运行时间：" + (endTime - startTime) + "ms");    //输出程序运行时间
		} catch (Exception e) {
			e.printStackTrace();
			return;
		}
		//判断是否有返回值
		if (content == null || "".equals(content)) {
			return;
		}
		try {
			new Receiver().log(content);
		} catch (Exception e) {
			e.printStackTrace();
		}

		//判断接口状态
		JSONObject json = JSONObject.fromObject(content);
		System.out.println("json.data.size="+json.getJSONArray("data").size());
		String code = json.getString("code");
		System.out.println("code="+code);

	}

	public static void main1(String[] args) {
		Receiver r = new Receiver();
		Map<String,String> map=new HashMap<String,String>();
		map.put("vid", "11");
		map.put("state", "1");
		map.put("level", "2");
		map.put("reply", "中文乱码么");
		String a=r.sender(map);
		System.out.println("********"+a);
	}

	public List<QihooModel> receive() {
		List<QihooModel> models = new ArrayList<QihooModel>();
		try {
			Receiver r = new Receiver();
			List<IDModel> list = r.receiveAll();
			for (IDModel model : list) {
				QihooModel m = r.receiveOne(model.getId());
				models.add(m);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return models;
	}

	public List<QihooModel> receiveFiveMinutesAdd() {
		List<QihooModel> models = new ArrayList<QihooModel>();
		try {
			Receiver r = new Receiver();
			models = r.receiveFiveMinutes();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return models;
	}

	private List<QihooModel> receiveFiveMinutes() throws Exception {
		List<QihooModel> resultList = new ArrayList<QihooModel>();
		String url = getaddUrl();
		String result = getReturnData(url);
		log(result);
		QihooModel[] models = JsonUtil.load(result, QihooModel[].class);
		if (models != null) {
			resultList = Arrays.asList(models);
		}
		return resultList;
	}
	/**
	 * 新增addcert接口
	 * @return
	 * @throws Exception
	 */
	private String[] receiveCertThreeMinutes() throws Exception {
		String url = getaddCert();
		String result = getReturnData(url);
		log(result);
		String[] ids = JsonUtil.load(result, String[].class);
		return ids;
	}

	/**
	 * 新修改全量接口
	 * @return
	 * @throws Exception
	 */
	private String[] receiveCertAll() throws Exception {
		String url = getallUrl();
		String result = getReturnData(url);
		log(result);
		System.out.println(result);
		String[] ids = JsonUtil.load(result, String[].class);
		return ids;
	}


	private List<IDModel> receiveAll() throws Exception {
		String url = getallUrl();
		String result = getReturnData(url);
		log(result);
		List<IDModel> models = receiveAll(result);
		return models;
	}

	private List<IDModel> receiveAll(String result) {
		List<IDModel> models = new ArrayList<IDModel>();
		if (result == null) {
			return models;
		}
		IDModel[] modelsTemp = JsonUtil.load(result, IDModel[].class);
		if (modelsTemp == null) {
			return models;
		}
		models = Arrays.asList(modelsTemp);
		return models;
	}

	private QihooModel receiveOne(String id) throws Exception {
		String url = getoneUrl(id);
		System.out.println(url);
		String result = getReturnData(url);
		// log
		log(result);

		QihooModel model = new QihooModel();
		QihooModel[] models = JsonUtil.load(result, QihooModel[].class);

		if (models == null || models.length == 0) {
			return null;
		}
		model = models[0];
		return model;
	}

	private String getReturnData(String urlString) throws Exception {
		String res = "";
		urlString = urlString + "&time=" + new Date().getTime();
		URL url = new URL(urlString);
		java.net.HttpURLConnection conn = (java.net.HttpURLConnection) url
				.openConnection();
		conn.setDoOutput(true);
		conn.setRequestMethod("GET");
		BufferedReader in = new BufferedReader(
				new InputStreamReader(conn.getInputStream(), "UTF-8"));
		String line;
		while ((line = in.readLine()) != null) {
			res += line;
		}
		in.close();
		return res;
	}

	public String sender(Map<String,String> data){
		return sender(getSenderUrl(),data);
	}

	public static String sender(String url,Map<String,String> data){
		String status="0";
		CloseableHttpClient httpClient = HttpClients.createDefault();
		try {
			HttpPost httpPost = new HttpPost(url);
			List<NameValuePair> nvps = new ArrayList<NameValuePair>();
			if (data != null && data.size() > 0) {
				for (String key : data.keySet()) {
					nvps.add(new BasicNameValuePair(key, (String) data
							.get(key)));
				}
				httpPost.setEntity(new UrlEncodedFormEntity(nvps,"UTF-8"));
			}
			CloseableHttpResponse response = httpClient.execute(httpPost);

			try {
				HttpEntity httpEntity = response.getEntity();
				if (httpEntity != null) {
					try {
						BufferedReader bufferedReader = new BufferedReader(
								new InputStreamReader( response.getEntity().getContent(),"UTF-8"), 1 * 1024);
						StringBuilder entityStringBuilder = new StringBuilder();
						String line = null;
						while ((line = bufferedReader.readLine()) != null) {
							entityStringBuilder.append(line);
							break;
						}
						// 利用从HttpEntity中得到的String生成JsonObject
						String json=entityStringBuilder.toString();
						if(json!=null){
							SenderQihoo senderQihoo = null;
							try {
								senderQihoo = JsonUtil.load(json, SenderQihoo.class);
							} catch (Exception e) {
								// TODO Auto-generated catch block
//								e.printStackTrace();
//								System.out.println("http://butian.360.cn/api/invul接口出错，无法访问1");
							}
							if(senderQihoo.getState()!=null && senderQihoo.getState().intValue()==1){
								status="1";
							}else if(senderQihoo.getMsg()!=null && "".equals(senderQihoo.getMsg())){
								status=senderQihoo.getMsg();
							}/*lse if(senderQihoo.getVid()!=null && "".equals(senderQihoo.getVid())){
								vid=senderQihoo.getVid();
						}*/
						}

					} catch (Exception e) {
//						e.printStackTrace();
//						System.out.println("发送失败！");
					}
				}
			} finally {
				response.close();
			}
		} catch (Exception e) {
//			e.printStackTrace();
			System.out.println("http://butian.360.cn/api/invul接口出错，无法访问2");
		} finally {
			try {
				httpClient.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return status;
	}

	private String getSenderUrl() {
		String url = getSender + "?t=" + key();
		return url;
	}

	private String getallUrl() {
		String url = getall + "?t=" + key();
		return url;
	}

	private String getaddUrl() {
		String url = getadd + "?t=" + key();
		return url;
	}
	private String getaddCert() {
		String url = getaddcert + "?t=" + key();
		return url;
	}

	private String getoneUrl(String id) {
		String url = getone + "?t=" + key() + "&id=" + id;
		return url;
	}

	private String key() {
		String now = now();
		String key = now + suffix;
		String md5Key = MD5.getMD5Str(key);
		return md5Key;
	}

	private String now() {
		Date now = new Date();
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		return formatter.format(now);
	}

	private void log(String content) throws Exception {
		File file = new File(Util.QIHOO_LOG_PATH);
		if (!file.exists()) {
			file.mkdirs();
		}
		Date date = new Date();
		String fileName = new SimpleDateFormat("yyyy-MM-dd").format(date)
				+ ".log";
		File logFile = new File(Util.QIHOO_LOG_PATH + File.separator + fileName);
//		String formatDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
//				.format(date);
		BufferedWriter bw = new BufferedWriter(new FileWriter(logFile, false));
//		bw.write(formatDate);
		bw.write(content);
		bw.close();
	}

	public static class IDModel {
		private String id;

		public String getId() {
			return id;
		}

		public void setId(String id) {
			this.id = id;
		}

		@Override
		public String toString() {
			return "IDModel [id=" + id + "]";
		}
	}

	public static class QihooModel {
		private String id;
		private String title;
		private String description;
		private String detail;
		private String prove;
		private String level;
		private String username;
		private String is_event;
		private String open_time;
		private String status;
		private String writetime;
		private String type2;

		public String getId() {
			return id;
		}

		public void setId(String id) {
			this.id = id;
		}

		public String getTitle() {
			return title;
		}

		public void setTitle(String title) {
			this.title = title;
		}

		public String getDescription() {
			return description;
		}

		public void setDescription(String description) {
			this.description = description;
		}

		public String getDetail() {
			return detail;
		}

		public void setDetail(String detail) {
			this.detail = detail;
		}

		public String getProve() {
			return prove;
		}

		public void setProve(String prove) {
			this.prove = prove;
		}

		public String getLevel() {
			return level;
		}

		public void setLevel(String level) {
			this.level = level;
		}

		public String getUsername() {
			return username;
		}

		public void setUsername(String username) {
			this.username = username;
		}

		public String getIs_event() {
			return is_event;
		}

		public void setIs_event(String is_event) {
			this.is_event = is_event;
		}

		public String getOpen_time() {
			return open_time;
		}

		public void setOpen_time(String open_time) {
			this.open_time = open_time;
		}

		public String getStatus() {
			return status;
		}

		public void setStatus(String status) {
			this.status = status;
		}

		public String getWritetime() {
			return writetime;
		}

		public void setWritetime(String writetime) {
			this.writetime = writetime;
		}

		public String getType2() {
			return type2;
		}

		public void setType2(String type2) {
			this.type2 = type2;
		}

		@Override
		public String toString() {
			return "QihooModel [id=" + id + ", title=" + title
					+ ", description=" + description + ", detail=" + detail
					+ ", prove=" + prove + ", level=" + level + ", username="
					+ username + ", is_event=" + is_event + ", open_time="
					+ open_time + ", status=" + status + ", writetime="
					+ writetime + ", type2=" + type2 + "]";
		}
	}
	public static class SenderQihoo {
		public Integer state;
		public String msg;
		public String vid;
		/*public String getVid() {
			return vid;
		}
		public void setVid(String vid) {
			this.vid = vid;
		}*/
		public Integer getState() {
			return state;
		}
		public void setState(Integer state) {
			this.state = state;
		}
		public String getMsg() {
			return msg;
		}
		public void setMsg(String msg) {
			this.msg = msg;
		}

	}
}
