package com.cnvd.util

import java.text.SimpleDateFormat;

import java.text.SimpleDateFormat

import jxl.Workbook
import jxl.WorkbookSettings
import jxl.write.Label
import jxl.write.Number
import jxl.write.WritableCellFormat
import jxl.write.WritableFont
import jxl.write.WritableHyperlink
import jxl.write.WritableSheet
import jxl.write.WritableWorkbook

import com.cnvd.flawInfo.DictionaryInfo
import com.cnvd.flawInfo.ExmaineHistory
import com.cnvd.flawInfo.Exploit
import com.cnvd.flawInfo.FlawProduct
import com.cnvd.flawInfo.ReferenceInfo
import com.cnvd.flawInfo.ReferenceType
import com.cnvd.patchInfo.PatchInfo

class ReportUtil {
	public static exportIndustryFlaw(def list){
		WorkbookSettings workbookSettings = new WorkbookSettings()
		workbookSettings.locale = Locale.default

		def file = File.createTempFile('myExcelDocument', '.xls')
		file.deleteOnExit()

		WritableWorkbook workbook = Workbook.createWorkbook(file, workbookSettings)

		WritableFont font = new WritableFont(WritableFont.ARIAL, 12)
		WritableCellFormat format = new WritableCellFormat(font)

		def row = 0
		WritableSheet sheet = workbook.createSheet('漏洞信息', 0)
		WritableSheet sheet1 = workbook.createSheet('漏洞评分信息', 1)
		sheet.addCell(new Label(0, row, "漏洞名称" , format))
		sheet.addCell(new Label(1, row, "危害等级", format))
		sheet.addCell(new Label(2, row, "类别", format))
		sheet.addCell(new Label(3, row, "生产厂商", format))
		sheet.addCell(new Label(4, row, "cvss评分", format))
		sheet.addCell(new Label(5, row, "漏洞描述", format))
		sheet.addCell(new Label(6, row, "参考链接", format))
		sheet1.addCell(new Label(0, row,"漏洞名称" , format))
		sheet1.addCell(new Label(1, row, "综合等级", format))
		sheet1.addCell(new Label(2, row, "cvss评分", format))
		sheet1.addCell(new Label(3, row, "攻击途径", format))
		sheet1.addCell(new Label(4, row, "攻击复杂度", format))
		sheet1.addCell(new Label(5, row, "攻击前提(需要通过认证的程度)", format))
		sheet1.addCell(new Label(6, row, "对保密性的影响", format))
		sheet1.addCell(new Label(7, row, "对完整性的影响", format))
		sheet1.addCell(new Label(8, row, "对可用性的影响", format))
		
		row++
		list.each {
			String titleStr=it.title.toString()
			String serverityStr=DictionaryInfo.get(it?.serverityId)?DictionaryInfo.get(it?.serverityId)?.name.toString():"未评级"
			String softStyleStr=DictionaryInfo.get(it?.softStyleId)?DictionaryInfo.get(it?.softStyleId)?.name.toString():"未评级"
			String manuNameStr = it.manufacturer? it.manufacturer.name : ""
			String basemetricScoreStr=it.basemetric==null ? "未打分":it.basemetric.score.toString()
			String desc = it.detailedInfo.description
			String referenceLinkStr = it.referenceLink? it.referenceLink : ""
			
			sheet.addCell(new Label(0, row, titleStr , format))
			sheet.addCell(new Label(1, row, serverityStr, format))
			sheet.addCell(new Label(2, row, softStyleStr, format))
			sheet.addCell(new Label(3, row, manuNameStr, format))
			sheet.addCell(new Label(4, row, basemetricScoreStr, format))
			sheet.addCell(new Label(5, row, desc, format))
			sheet.addCell(new Label(6, row, referenceLinkStr, format))
			sheet1.addCell(new Label(0, row, titleStr, format))//漏洞名称
			sheet1.addCell(new Label(1, row, serverityStr, format))//综合等级
			sheet1.addCell(new Label(2, row, basemetricScoreStr , format))//cvss评分
			sheet1.addCell(new Label(3, row, it.basemetric?it.basemetric.accessVector.name:"未知" , format))//攻击途径
			sheet1.addCell(new Label(4, row, it.basemetric?it.basemetric.accessComplexity.name:"未知" , format))//攻击复杂度
			sheet1.addCell(new Label(5, row, it.basemetric?it.basemetric.authentication.name:"未知" , format))//攻击前提(需要通过认证的程度)
			sheet1.addCell(new Label(6, row, it.basemetric?it.basemetric.confidentialityImpact.name:"未知" , format))//对保密性的影响
			sheet1.addCell(new Label(7, row, it.basemetric?it.basemetric.integrityImpact.name:"未知" , format))//对完整性的影响
			sheet1.addCell(new Label(8, row, it.basemetric?it.basemetric.availabilityImpact.name:"未知" , format))//对可用性的影响
			
			row++
		}
		workbook.write()
		workbook.close()
		return file
	}
	
	public static createFlawInfoExcelReport(def list) {
		println "list="+list
		WorkbookSettings workbookSettings = new WorkbookSettings()
		workbookSettings.locale = Locale.default

		def file = File.createTempFile('myExcelDocument', '.xls')
		file.deleteOnExit()

		WritableWorkbook workbook = Workbook.createWorkbook(file, workbookSettings)

		WritableFont font = new WritableFont(WritableFont.ARIAL, 12)
		WritableCellFormat format = new WritableCellFormat(font)

		def row = 0
		WritableSheet sheet = workbook.createSheet('漏洞信息', 0)
		WritableSheet sheet1 = workbook.createSheet('漏洞评分信息', 1)
		sheet.addCell(new Label(0, row,"时间" , format))
		sheet.addCell(new Label(1, row, "漏洞名称", format))
		sheet.addCell(new Label(2, row, "危害等级", format))
		sheet.addCell(new Label(3, row, "cvss评分", format))
		sheet.addCell(new Label(4, row, "漏洞报送者", format))
		sheet.addCell(new Label(5, row, "是否为自主发现漏洞", format))
		sheet.addCell(new Label(6, row, "备注", format))
		sheet.addCell(new Label(7, row, "参考链接", format))
		sheet1.addCell(new Label(0, row,"漏洞名称" , format))
		sheet1.addCell(new Label(1, row, "综合等级", format))
		sheet1.addCell(new Label(2, row, "cvss评分", format))
		sheet1.addCell(new Label(3, row, "攻击途径", format))
		sheet1.addCell(new Label(4, row, "攻击复杂度", format))
		sheet1.addCell(new Label(5, row, "攻击前提(需要通过认证的程度)", format))
		sheet1.addCell(new Label(6, row, "对保密性的影响", format))
		sheet1.addCell(new Label(7, row, "对完整性的影响", format))
		sheet1.addCell(new Label(8, row, "对可用性的影响", format))

		row++
		list.each {
			SimpleDateFormat sdf =new SimpleDateFormat("yyyy.MM.dd")
			String dateCreatedStr=sdf.format(it.dateCreated)
			String titleStr=it.title.toString()
			String serverityStr=DictionaryInfo.get(it?.serverityId)?DictionaryInfo.get(it?.serverityId)?.name.toString():"未评级"
			String basemetricScoreStr=it.basemetric==null ? "未打分":it.basemetric.score.toString()
			String isOriginalStr="未知"
			if(it.isOriginal==0){
				isOriginalStr="是"
			}else if(it.isOriginal==1){
				isOriginalStr="否"
			}
			sheet.addCell(new Label(0, row,dateCreatedStr , format))
			sheet.addCell(new Label(1, row, titleStr, format))
			sheet.addCell(new Label(2, row, serverityStr, format))
			sheet.addCell(new Label(3, row, basemetricScoreStr, format))
			sheet.addCell(new Label(4, row, it.user.nickName, format))
			sheet.addCell(new Label(5, row, isOriginalStr, format))
			sheet.addCell(new Label(6, row, "", format))
			if(it.referenceLink){
				sheet.addHyperlink(new WritableHyperlink(7,row,new URL(it.referenceLink.split("\n")[0]))) //取参考链接中的第一条
			}else{
				sheet.addCell(new Label(7, row, "", format))
			}
			
			sheet1.addCell(new Label(0, row,titleStr, format))//漏洞名称
			sheet1.addCell(new Label(1, row,serverityStr, format))//综合等级
			sheet1.addCell(new Label(2, row,basemetricScoreStr , format))//cvss评分
			sheet1.addCell(new Label(3, row,it.basemetric?it.basemetric.accessVector.name:"未知" , format))//攻击途径
			sheet1.addCell(new Label(4, row,it.basemetric?it.basemetric.accessComplexity.name:"未知" , format))//攻击复杂度
			sheet1.addCell(new Label(5, row,it.basemetric?it.basemetric.authentication.name:"未知" , format))//攻击前提(需要通过认证的程度)
			sheet1.addCell(new Label(6, row,it.basemetric?it.basemetric.confidentialityImpact.name:"未知" , format))//对保密性的影响
			sheet1.addCell(new Label(7, row,it.basemetric?it.basemetric.integrityImpact.name:"未知" , format))//对完整性的影响
			sheet1.addCell(new Label(8, row,it.basemetric?it.basemetric.availabilityImpact.name:"未知" , format))//对可用性的影响
			row++
		}
		workbook.write()
		workbook.close()
		return file
	}
	
	public static createHighLevelAndPOCFlawExcel(def list1,def list2){
		WorkbookSettings workbookSettings = new WorkbookSettings()
		workbookSettings.locale = Locale.default

		def file = File.createTempFile('myExcelDocument', '.xls')
		file.deleteOnExit()

		WritableWorkbook workbook = Workbook.createWorkbook(file, workbookSettings)

		WritableFont font1 = new WritableFont(WritableFont.ARIAL, 12,WritableFont.BOLD)
		WritableCellFormat format1 = new WritableCellFormat(font1)
		WritableFont font2 = new WritableFont(WritableFont.ARIAL, 12)
		WritableCellFormat format2 = new WritableCellFormat(font2)

		def row = 0
		WritableSheet sheet = workbook.createSheet('Sheet1', 0)
		sheet.addCell(new Label(0, row,"时间" , format1))
		sheet.addCell(new Label(1, row, "漏洞名称", format1))
		sheet.addCell(new Label(2, row, "危险等级", format1))
		sheet.addCell(new Label(3, row, "漏洞描述", format1))
		sheet.addCell(new Label(4, row, "危害", format1))
		sheet.addCell(new Label(5, row, "攻击位置", format1))
		sheet.addCell(new Label(6, row, "是否有攻击代码", format1))
		sheet.addCell(new Label(7, row, "验证信息参考", format1))
		sheet.addCell(new Label(8, row, "验证POC", format1))

		row++
		list1.each {
			SimpleDateFormat sdf =new SimpleDateFormat("yyyy.MM.dd")
			def flaw = it.flaw
			String dateCreatedStr=sdf.format(flaw.dateCreated)
			String titleStr=flaw.title.toString()
			String serverityStr=DictionaryInfo.get(flaw.serverityId)?.name.toString()
			String descStr = flaw.detailedInfo?.description
			String attackAdd = DictionaryInfo.get(flaw.positionId)?.name.toString()
			String referenceLink = it.referenceLink
			String pocStr = it.poc
			
			sheet.addCell(new Label(0, row,dateCreatedStr , format2))
			sheet.addCell(new Label(1, row, titleStr, format2))
			sheet.addCell(new Label(2, row, serverityStr, format2))
			sheet.addCell(new Label(3, row, descStr, format2))
			sheet.addCell(new Label(4, row, "", format2))
			sheet.addCell(new Label(5, row, attackAdd, format2))
			sheet.addCell(new Label(6, row, "是", format2))
			if(it.referenceLink){
				sheet.addHyperlink(new WritableHyperlink(7,row,new URL(it.referenceLink.split("\n")[0]))) //取参考链接中的第一条
			}else{
				sheet.addCell(new Label(7, row, "", format2))
			}
			//sheet.addCell(new Label(7, row, referenceLink, format2)) //验证信息参考链接
			sheet.addCell(new Label(8, row, pocStr, format2))
			row++
		}
		list2.each {
			SimpleDateFormat sdf =new SimpleDateFormat("yyyy.MM.dd")
			String dateCreatedStr=sdf.format(it.dateCreated)
			String titleStr=it.title.toString()
			String serverityStr=DictionaryInfo.get(it?.serverityId)?.name.toString()
			String descStr = it.detailedInfo?.description
			String attackAdd = DictionaryInfo.get(it.positionId)?.name.toString()
			
			sheet.addCell(new Label(0, row,dateCreatedStr , format2))
			sheet.addCell(new Label(1, row, titleStr, format2))
			sheet.addCell(new Label(2, row, serverityStr, format2))
			sheet.addCell(new Label(3, row, descStr, format2))
			sheet.addCell(new Label(4, row, "", format2))
			sheet.addCell(new Label(5, row, attackAdd, format2))
			sheet.addCell(new Label(6, row, "否", format2))
			sheet.addCell(new Label(7, row, "", format2))
			sheet.addCell(new Label(8, row, "", format2))
			row++
		}
		workbook.write()
		workbook.close()
		return file
	}
	
	public static createReportDetailExcel(def list){
		WorkbookSettings workbookSettings = new WorkbookSettings()
		workbookSettings.locale = Locale.default
		SimpleDateFormat sdf =  new SimpleDateFormat("yyyy-MM-dd");

		def file = File.createTempFile('myExcelDocument', '.xlsx')
		file.deleteOnExit()

		WritableWorkbook workbook = Workbook.createWorkbook(file, workbookSettings)

		WritableFont font1 = new WritableFont(WritableFont.ARIAL, 12)
		WritableCellFormat format = new WritableCellFormat(font1)

		def row = 0
		WritableSheet sheet = workbook.createSheet('漏洞接收情况明细', 0)
		sheet.addCell(new Label(0, row,"漏洞编号" , format))
		sheet.addCell(new Label(1, row, "关联漏洞编号", format))
		sheet.addCell(new Label(2, row, "漏洞名称", format))
		sheet.addCell(new Label(3, row, "漏洞描述", format))
		sheet.addCell(new Label(4, row, "报送单位", format))
		sheet.addCell(new Label(5, row, "是否为原创漏洞", format))
		sheet.addCell(new Label(6, row, "是否列入处置", format))
		sheet.addCell(new Label(7, row, "是否列入脚本", format))
		sheet.addCell(new Label(8, row, "临时解决办法", format))
		sheet.addCell(new Label(9, row, "正式解决办法", format))
		sheet.addCell(new Label(10, row, "产生原因", format))
		sheet.addCell(new Label(11, row, "漏洞危害等级", format))
		sheet.addCell(new Label(12, row, "漏洞引发的威胁", format))
		sheet.addCell(new Label(13, row, "漏洞攻击位置", format))
		sheet.addCell(new Label(14, row, "漏洞影响对象类型", format))
		sheet.addCell(new Label(15, row, "发现者", format))
		sheet.addCell(new Label(16, row, "外部引用编号（）", format))
		sheet.addCell(new Label(17, row, "漏洞参考链接", format))
		sheet.addCell(new Label(18, row, "验证信息参考链接", format))
		sheet.addCell(new Label(19, row, "验证POC", format))
		sheet.addCell(new Label(20, row, "影响产品（厂商，产品，版本）", format))
		sheet.addCell(new Label(21, row, "补丁信息", format))
		sheet.addCell(new Label(22, row, "报送时间", format))
		sheet.addCell(new Label(23, row, "录入时间", format))
		
		row++
		list.each {
			def flaw = it
			def relationFlawNum = ""
			if(it.parentFlaw){
				//漏洞有关联漏洞
				flaw = it.parentFlaw
				if(flaw.status == 9){
					relationFlawNum = flaw.number
				}else{
					relationFlawNum = flaw.tempNumber?flaw.tempNumber:flaw.number
				}
			}else{
				relationFlawNum = "无"
			}
			def numberStr = ""
			if(it.status == 9){
				numberStr = it.number
			}else{
				numberStr = it.tempNumber?it.tempNumber:it.number
			}
			sheet.addCell(new Label(0, row, numberStr, format))
			sheet.addCell(new Label(1, row, relationFlawNum, format))//关联漏洞编号
			sheet.addCell(new Label(2, row, flaw.title, format))
			sheet.addCell(new Label(3, row, flaw.detailedInfo?.description, format))
			sheet.addCell(new Label(4, row, it.user.userName, format)) //报送单位
			def isOriginalStr = ""
			switch(flaw.isOriginal){
				case 0:isOriginalStr="是";break;
				case 1:isOriginalStr="否";break;
				case 2:isOriginalStr="未知";break;
			}
			sheet.addCell(new Label(5, row, isOriginalStr, format))//isOriginal
			sheet.addCell(new Label(6, row, "否", format))
			sheet.addCell(new Label(7, row, "否", format))
			sheet.addCell(new Label(8, row, flaw.detailedInfo?.tempWay?flaw.detailedInfo?.tempWay:"无", format))
			sheet.addCell(new Label(9, row, flaw.detailedInfo?.formalWay?flaw.detailedInfo?.formalWay:"无" , format))
			sheet.addCell(new Label(10, row, DictionaryInfo.get(flaw.causeId)?.name, format))
			sheet.addCell(new Label(11, row, DictionaryInfo.get(flaw.serverityId)?.name, format))
			sheet.addCell(new Label(12, row, DictionaryInfo.get(flaw.threadId)?.name, format))
			sheet.addCell(new Label(13, row, DictionaryInfo.get(flaw.positionId)?.name, format))
			sheet.addCell(new Label(14, row, DictionaryInfo.get(flaw.softStyleId)?.name, format))
			sheet.addCell(new Label(15, row, flaw.discovererName, format))
			def referenceInfoList = ReferenceInfo.findAllByFlaw(flaw)
			def referenceNumStr = ""
			referenceInfoList.each{
				referenceNumStr += it.referenceNumber+"\n"
			}
			sheet.addCell(new Label(16, row, referenceNumStr, format)) //外部引用编号
			sheet.addCell(new Label(17, row, flaw.referenceLink, format))
			def exploit = Exploit.findByFlaw(flaw)
			def pocStr = ""
			if(exploit && exploit.poc != null){
				if(exploit.poc.length()>=4000){
					pocStr = exploit.poc.substring(0,4000)
				}else{
					pocStr = exploit.poc
				}
			}else{
				pocStr = "暂无"
			}
			sheet.addCell(new Label(18, row, exploit?exploit.referenceLink:"暂无", format))
			sheet.addCell(new Label(19, row, pocStr, format))
			def reflectProductList = FlawProduct.findAllByFlaw(flaw)
			def reflectProductStr = ""
			reflectProductList.each{
				reflectProductStr += it.product.name+"\n"
			}
			sheet.addCell(new Label(20, row, reflectProductStr?reflectProductStr:"无", format))
			def patchInfo = PatchInfo.findByFlaw(flaw)
			sheet.addCell(new Label(21, row, patchInfo?"有":"无", format))
			sheet.addCell(new Label(22, row, sdf.format(it.submitTime), format))
			sheet.addCell(new Label(23, row, sdf.format(it.dateCreated), format))
			row++
		}
		workbook.write()
		workbook.close()
		return file
	}
	
	public static createFlawSearchResultReport(def list,def params) {
		println "list="+list
		println "reportutil params="+params
		WorkbookSettings workbookSettings = new WorkbookSettings()
		workbookSettings.locale = Locale.default

		def file = File.createTempFile('myExcelDocument', '.xls')
		file.deleteOnExit()

		WritableWorkbook workbook = Workbook.createWorkbook(file, workbookSettings)

		WritableFont font = new WritableFont(WritableFont.ARIAL, 12)
		WritableCellFormat format = new WritableCellFormat(font)

		def row = 0
		def columnNum = 0
		WritableSheet sheet = workbook.createSheet('漏洞信息', 0)
		if(params.numberChek){
			sheet.addCell(new Label(columnNum++, row, "漏洞编号", format))
		}
		if(params.titleChek){
			sheet.addCell(new Label(columnNum++, row, "漏洞标题", format))
		}
		if(params.descriptionChek){
			sheet.addCell(new Label(columnNum++, row, "漏洞描述", format))
		}
		if(params.formalWayChek){
			sheet.addCell(new Label(columnNum++, row, "正式解决方案", format))
		}
		if(params.tempWayChek){
			sheet.addCell(new Label(columnNum++, row, "临时解决方案", format))
		}
		if(params.isFirstChek){
			sheet.addCell(new Label(columnNum++, row, "是否首次公开", format))
		}
		if(params.isZeroChek){
			sheet.addCell(new Label(columnNum++, row, "是否零日漏洞", format))
		}
		if(params.dateCreatedChek){
			sheet.addCell(new Label(columnNum++, row, "录入时间", format))
		}
		if(params.submitTimeChek){
			sheet.addCell(new Label(columnNum++, row, "报送时间", format))
		}
		if(params.storageTimeChek){
			sheet.addCell(new Label(columnNum++, row, "归档时间", format))
		}
		if(params.openTimeChek){
			sheet.addCell(new Label(columnNum++, row, "公开时间", format))
		}
		if(params.statusChek){
			sheet.addCell(new Label(columnNum++, row, "当前状态", format))
		}
		if(params.enableChek){
			sheet.addCell(new Label(columnNum++, row, "前台是否可见", format))
		}
		if(params.causeIdChek){
			sheet.addCell(new Label(columnNum++, row, "漏洞产生原因", format))
		}
		if(params.threadIdChek){
			sheet.addCell(new Label(columnNum++, row, "漏洞引发的威胁", format))
		}
		if(params.serverityIdChek){
			sheet.addCell(new Label(columnNum++, row, "漏洞严重程度", format))
		}
		if(params.positionIdChek){
			sheet.addCell(new Label(columnNum++, row, "漏洞利用的攻击位置", format))
		}
		if(params.softStyleIdChek){
			sheet.addCell(new Label(columnNum++, row, "漏洞影响对象类型", format))
		}
		if(params.isuChek){
			sheet.addCell(new Label(columnNum++, row, "是否前台上报", format))
		}
		if(params.userChek){
			sheet.addCell(new Label(columnNum++, row, "报送人", format))
		}
		if(params.isHotChek){
			sheet.addCell(new Label(columnNum++, row, "是否是热点漏洞", format))
		}
		if(params.isOriginalChek){
			sheet.addCell(new Label(columnNum++, row, "是否原创", format))
		}
		if(params.discovererNameChek){
			sheet.addCell(new Label(columnNum++, row, "发现者姓名", format))
		}
		if(params.isvChek){
			sheet.addCell(new Label(columnNum++, row, "是否验证 ", format))
		}
		if(params.exploitTypeChek){
			sheet.addCell(new Label(columnNum++, row, "验证类型 ", format))
		}
		if(params.exploitNameChek){
			sheet.addCell(new Label(columnNum++, row, "验证名称 ", format))
		}
		if(params.exploitUserChek){
			sheet.addCell(new Label(columnNum++, row, "前台验证用户 ", format))
		}
		if(params.exploitConceptChek){
			sheet.addCell(new Label(columnNum++, row, "验证原理 ", format))
		}
		if(params.exploitPocChek){
			sheet.addCell(new Label(columnNum++, row, "验证poc", format))
		}
		if(params.exploitSuggChek){
			sheet.addCell(new Label(columnNum++, row, "验证建议", format))
		}
		if(params.exploitTimeChek){
			sheet.addCell(new Label(columnNum++, row, "验证日期", format))
		}
		if(params.exploitStatusChek){
			sheet.addCell(new Label(columnNum++, row, "验证状态", format))
		}
		if(params.exploitContentChek){
			sheet.addCell(new Label(columnNum++, row, "验证审核意见", format))
		}
		if(params.exploitRefChek){
			sheet.addCell(new Label(columnNum++, row, "验证信息参考链接", format))
		}
		if(params.ivpChek){
			sheet.addCell(new Label(columnNum++, row, "是否处置", format))
		}
		if(params.patchIdChek){
			sheet.addCell(new Label(columnNum++, row, "补丁编号", format))
		}
		if(params.patchNameChek){
			sheet.addCell(new Label(columnNum++, row, "补丁名称", format))
		}
		if(params.patchValidChek){
			sheet.addCell(new Label(columnNum++, row, "补丁信息", format))
		}
		if(params.patchUserChek){
			sheet.addCell(new Label(columnNum++, row, "补丁贡献者", format))
		}
		if(params.patchDesChek){
			sheet.addCell(new Label(columnNum++, row, "补丁描述", format))
		}
		if(params.patchFunChek){
			sheet.addCell(new Label(columnNum++, row, "补丁验证原理", format))
		}
		if(params.patchUrlChek){
			sheet.addCell(new Label(columnNum++, row, "补丁链接", format))
		}
		if(params.patchSourceChek){
			sheet.addCell(new Label(columnNum++, row, "补丁来源", format))
		}
		if(params.patchStatusChek){
			sheet.addCell(new Label(columnNum++, row, "补丁状态", format))
		}
		if(params.referTypeChek){
			sheet.addCell(new Label(columnNum++, row, "外部引用", format))
		}
		if(params.reflectProductChek){
			sheet.addCell(new Label(columnNum++, row, "影响产品", format))
		}
		if(params.scoreChek){
			sheet.addCell(new Label(columnNum++, row, "评分(基本度量/时间度量/环境度量)", format))
		}
		if(params.scoreDetailChek){
			sheet.addCell(new Label(columnNum++, row, "攻击途径", format))
			sheet.addCell(new Label(columnNum++, row, "攻击复杂度", format))
			sheet.addCell(new Label(columnNum++, row, "认证", format))
			sheet.addCell(new Label(columnNum++, row, "机密性", format))
			sheet.addCell(new Label(columnNum++, row, "完整性", format))
			sheet.addCell(new Label(columnNum++, row, "可用性", format))
		}
		if(params.hangyeChek){
			sheet.addCell(new Label(columnNum++, row, "行业", format))
		}
		if(params.referenceLinkChek){
			sheet.addCell(new Label(columnNum++, row, "引用链接", format))
		}
		if(params.exmaineContentChek){
			sheet.addCell(new Label(columnNum++, row, "审核意见", format))
		}
		row++
		list.each {
			columnNum = 0
			def flaw = it
			SimpleDateFormat sdf =new SimpleDateFormat("yyyy.MM.dd")
			if(params.numberChek){
				def numberStr = flaw.status==9?(flaw.number):(flaw.tempNumber?flaw.tempNumber:flaw.number)
				sheet.addCell(new Label(columnNum++, row, numberStr, format))//漏洞编号
			}
			
			if(params.titleChek){
				sheet.addCell(new Label(columnNum++, row, flaw.title, format))//漏洞标题
			}
			
			if(params.descriptionChek){
				sheet.addCell(new Label(columnNum++, row, flaw.detailedInfo?.description, format))//漏洞描述
			}
			
			if(params.formalWayChek){
				sheet.addCell(new Label(columnNum++, row, flaw.detailedInfo?.formalWay, format))//正式解决方案
			}
			
			if(params.tempWayChek){
				sheet.addCell(new Label(columnNum++, row, flaw.detailedInfo?.tempWay, format))//临时解决方案
			}
			
			if(params.isFirstChek){
				def isFirstStr = ""
				if(flaw.isFirst==0){
					isFirstStr = "是"
				}else if(flaw.isFirst==1){
					isFirstStr = "否"
				}
				sheet.addCell(new Label(columnNum++, row, isFirstStr, format))//是否首次公开
			}
			
			if(params.isZeroChek){
				def isZeroStr = ""
				if(flaw.isZero==1){
					isZeroStr="是"
				}else if(flaw.isZero == 0){
					isZeroStr = "否"
				}
				sheet.addCell(new Label(columnNum++, row, isZeroStr, format))//是否零日漏洞
			}
			
			if(params.dateCreatedChek){
				sheet.addCell(new Label(columnNum++, row, flaw.dateCreated?sdf.format(flaw.dateCreated):"", format))//发现时间
			}
			
			if(params.submitTimeChek){
				sheet.addCell(new Label(columnNum++, row, flaw.submitTime?sdf.format(flaw.submitTime):"", format))//报送时间
			}
			
			if(params.storageTimeChek){
				sheet.addCell(new Label(columnNum++, row, flaw.storageTime?sdf.format(flaw.storageTime):"", format))//归档时间
			}
			
			if(params.openTimeChek){
				sheet.addCell(new Label(columnNum++, row, flaw.openTime?sdf.format(flaw.openTime):"", format))//公开时间
			}
			
			if(params.statusChek){
				sheet.addCell(new Label(columnNum++, row, flaw.statusStr(), format))//当前状态
			}
			
			if(params.enableChek){
				def enableStr = ""
				if(flaw.enable == 1){
					enableStr="是"
				}else if(flaw.enable == 0){
					enableStr= "否"
				}
				sheet.addCell(new Label(columnNum++, row, enableStr, format))//前台是否可见
			}
			
			if(params.causeIdChek){
				sheet.addCell(new Label(columnNum++, row, DictionaryInfo.get(flaw.causeId)?DictionaryInfo.get(flaw.causeId).name:"暂无", format))//漏洞产生原因
			}
			
			if(params.threadIdChek){
				sheet.addCell(new Label(columnNum++, row, DictionaryInfo.get(flaw.threadId)?DictionaryInfo.get(flaw.threadId).name:"暂无", format))//漏洞引发的威胁
			}
			
			if(params.serverityIdChek){
				sheet.addCell(new Label(columnNum++, row, DictionaryInfo.get(flaw.serverityId)?DictionaryInfo.get(flaw.serverityId).name:"暂无", format))//漏洞严重程度
			}
			
			if(params.positionIdChek){
				sheet.addCell(new Label(columnNum++, row, DictionaryInfo.get(flaw.positionId)?DictionaryInfo.get(flaw.positionId).name:"暂无", format))//漏洞利用的攻击位置
			}
			
			if(params.softStyleIdChek){
				sheet.addCell(new Label(columnNum++, row, DictionaryInfo.get(flaw.softStyleId)?DictionaryInfo.get(flaw.softStyleId).name:"暂无", format))//漏洞影响对象类型
			}
			
			if(params.isuChek){
				if(flaw.isu==0){
					sheet.addCell(new Label(columnNum++, row, "是", format))//是否前台上报
				}else{
					sheet.addCell(new Label(columnNum++, row, "否", format))//是否前台上报
				}
			}
			
			if(params.userChek){
				if(flaw.isu==1){
					sheet.addCell(new Label(columnNum++, row, flaw.creator?.userName, format))//报送人
				}else{
					sheet.addCell(new Label(columnNum++, row, flaw.user?.nickName, format))//报送人
				}
			}
			
			if(params.isHotChek){
				def isHotStr = ""
				if(flaw.isHot==1){
					isHotStr="是"
				}else if(flaw.isHot==0){
					isHotStr="否"
				}
				sheet.addCell(new Label(columnNum++, row, isHotStr, format))//是否是热点漏洞
			}
			
			if(params.isOriginalChek){
				def isOriginalStr = ""
				if(flaw.isOriginal==0){
					isOriginalStr="是"
				}else if(flaw.isOriginal==1){
					isOriginalStr="否"
				}else if(flaw.isOriginal==2){
					isOriginalStr="未知"
				}
				sheet.addCell(new Label(columnNum++, row, isOriginalStr, format))//是否原创
			}
			
			if(params.discovererNameChek){
				sheet.addCell(new Label(columnNum++, row, flaw.discovererName, format))//发现者姓名
			}
			
			if(params.isvChek){
				def isvStr = ""
				if(flaw.isv==1){
					isvStr="是"
				}else if(flaw.isv==0){
					isvStr="否"
				}
				sheet.addCell(new Label(columnNum++, row, isvStr, format))//是否验证
			}
			
			def exploit = Exploit.findByFlaw(flaw)
			if(params.exploitTypeChek){
				def exploitTypeStr = ""
				if(exploit?.exploitType==1){
					exploitTypeStr="后台"
				}else if(exploit?.exploitType==2){
					exploitTypeStr="前台"
				}else if(exploit?.exploitType==3){
					exploitTypeStr="后台代替前台"
				}
				sheet.addCell(new Label(columnNum++, row, exploitTypeStr, format))//验证类型
			}
			
			if(params.exploitNameChek){
				sheet.addCell(new Label(columnNum++, row, exploit?.exploitName?exploit?.exploitName:"", format))//验证名称
			}
			
			if(params.exploitUserChek){
				sheet.addCell(new Label(columnNum++, row, exploit?.tuser?.nickName?exploit?.tuser?.nickName:"", format))//前台验证用户
			}
			
			if(params.exploitConceptChek){
				sheet.addCell(new Label(columnNum++, row, exploit?.concept?exploit?.concept:"", format))//验证原理
			}
			
			if(params.exploitPocChek){
				sheet.addCell(new Label(columnNum++, row, exploit?.poc?exploit?.poc:"", format))//验证poc
			}
			
			if(params.exploitSuggChek){
				sheet.addCell(new Label(columnNum++, row, exploit?.suggestion?exploit?.suggestion:"", format))//验证建议
			}
			
			if(params.exploitTimeChek){
				sheet.addCell(new Label(columnNum++, row, exploit?sdf.format(exploit.exploitTime):"", format))//验证日期
			}
				
			if(params.exploitStatusChek){
				def exploitStatusStr = ""
				if(exploit?.status == 1){
					exploitStatusStr="通过审核"
				}else if(exploit?.status == 2){
					exploitStatusStr="等待审核"
				}else if(exploit?.status == 3){
					exploitStatusStr="未通过审核"
				}else if(exploit?.status == 4){
					exploitStatusStr="未提交"
				}
				sheet.addCell(new Label(columnNum++, row, exploitStatusStr, format))//验证状态
			}
			
			if(params.exploitContentChek){
				sheet.addCell(new Label(columnNum++, row, exploit?.content?exploit?.content:"", format))//审核意见
			}
			
			if(params.exploitRefChek){
				sheet.addCell(new Label(columnNum++, row, exploit?.referenceLink?exploit?.referenceLink:"", format))//验证信息参考链接
			}
			
			if(params.ivpChek){
				def ivpStr = ""
				if(flaw.ivp==1){
					ivpStr="是"
				}else if(flaw.ivp==0){
					ivpStr="否"
				}
				sheet.addCell(new Label(columnNum++, row, ivpStr, format))//是否处置
			}
			
			def patchInfo = PatchInfo.findByFlaw(flaw)
			if(params.patchIdChek){
				sheet.addCell(new Label(columnNum++, row, patchInfo?patchInfo.patchId:"", format)) //补丁编号
			}
			
			if(params.patchNameChek){
				sheet.addCell(new Label(columnNum++, row, patchInfo?.patchName?patchInfo?.patchName:"", format))//补丁名称
			}
			
			if(params.patchValidChek){
				sheet.addCell(new Label(columnNum++, row, patchInfo?.valid?patchInfo?.valid:"", format)) //补丁信息
			}
			
			if(params.patchUserChek){
				sheet.addCell(new Label(columnNum++, row, patchInfo?.tuser?patchInfo?.tuser?.nickName:"", format)) //补丁贡献者
			}
			
			if(params.patchDesChek){
				sheet.addCell(new Label(columnNum++, row, patchInfo?.patchDescription?patchInfo?.patchDescription:"", format)) //补丁描述
			}
			
			if(params.patchFunChek){
				sheet.addCell(new Label(columnNum++, row, patchInfo?.function?patchInfo?.function:"", format))//补丁验证原理
			}
			
			if(params.patchUrlChek){
				sheet.addCell(new Label(columnNum++, row, patchInfo?.patchUrl?patchInfo.patchUrl:"", format))//补丁链接
			}
			
			if(params.patchSourceChek){
				def createSourceStr = ""
				if("1".equals(patchInfo?.createSource)){
					createSourceStr="后台"
				}else if("2".equals(patchInfo?.createSource)){
					createSourceStr="前台"
				}else if("3".equals(patchInfo?.createSource)){
					createSourceStr="后台代前台"
				}
				sheet.addCell(new Label(columnNum++, row, createSourceStr, format)) //补丁来源
			}
			
			if(params.patchStatusChek){
				def patchStatusStr = ""
				if("0".equals(patchStatusStr)){
					patchStatusStr="未提交"
				}else if("1".equals(patchStatusStr)){
					patchStatusStr="已提交"
				}else if("2".equals(patchStatusStr)){
					patchStatusStr="审核未通过"
				}else if("3".equals(patchStatusStr)){
					patchStatusStr="审核通过"
				}
				sheet.addCell(new Label(columnNum++, row, patchStatusStr, format)) //补丁状态
			}
			
			if(params.referTypeChek){
				def referList = ReferenceInfo.findAllByFlaw(flaw)
				def referenceStr = ""
				referList.each{
					referenceStr += "类型:"+it.referenceType?.name+"编号:"+it.referenceNumber+"参考链接:"+it.linkUrl+"\n"
				}
				sheet.addCell(new Label(columnNum++, row, referenceStr, format))
			}
			
			if(params.reflectProductChek){
				def flawProductList = FlawProduct.findAllByFlaw(flaw)
				def reflectProductStr = ""
				flawProductList.each{
					reflectProductStr += it.product.manufacturer.name+"|"+
						it.product.productCategory.name+"|"+it.product.name+"\n"
				}
				sheet.addCell(new Label(columnNum++, row, reflectProductStr, format))
			}
			
			if(params.scoreChek){
				def baseScoreStr = flaw.basemetric?.score? flaw.basemetric?.score+"":"未评分"
				def tempScoreStr = flaw.temporalMetric?.score?flaw.temporalMetric?.score+"":"未评分"
				def envirScoreStr = flaw.environmentalMetric?.score?flaw.environmentalMetric?.score+"":"未评分"
				sheet.addCell(new Label(columnNum++, row, baseScoreStr+"/"+tempScoreStr+"/"+envirScoreStr, format))
			}
			if(params.scoreDetailChek){
				println "scoreDetailChek"
				//TODO 评分详情
				def accessVectorName = flaw.basemetric?flaw.basemetric.accessVector.name:''
				def accessComplexityName = flaw.basemetric?flaw.basemetric.accessComplexity.name:''
				def authenticationName = flaw.basemetric?flaw.basemetric.authentication.name:''
				def confidentialityImpactName = flaw.basemetric?flaw.basemetric.confidentialityImpact.name:''
				def integrityImpactName = flaw.basemetric?flaw.basemetric.integrityImpact.name:''
				def availabilityImpactName = flaw.basemetric?flaw.basemetric.availabilityImpact.name:''
				
				sheet.addCell(new Label(columnNum++, row, accessVectorName, format))
				sheet.addCell(new Label(columnNum++, row, accessComplexityName, format))
				sheet.addCell(new Label(columnNum++, row, authenticationName, format))
				sheet.addCell(new Label(columnNum++, row, confidentialityImpactName, format))
				sheet.addCell(new Label(columnNum++, row, integrityImpactName, format))
				sheet.addCell(new Label(columnNum++, row, availabilityImpactName, format))
			}
			if(params.hangyeChek){
				sheet.addCell(new Label(columnNum++, row, flaw.hangye, format))
			}
			if(params.referenceLinkChek){
				sheet.addCell(new Label(columnNum++, row, flaw.referenceLink, format))
			}
			if(params.exmaineContentChek){
				println "exmaineContentChek"
				//TODO 审核意见
				def exmaineHistoryList = ExmaineHistory.findAllByFlaw(flaw,[sort:"dateCreated",order:"asc"])
				def exmaineContentStr = ""
				exmaineHistoryList.each{
					exmaineContentStr += it.content+"|"
				}
				if(exmaineContentStr.length() > 1){
					exmaineContentStr = exmaineContentStr.substring(0, exmaineContentStr.length()-1)
				}
				sheet.addCell(new Label(columnNum++, row, exmaineContentStr, format))
			}
			println "reportUtil AAAAAAAAAAAAAAAAAAAAAAAAAAA"
			row++
		}
		workbook.write()
		workbook.close()
		return file
	}
	
	/**
	 * 生成excel表格
	 * @param flawRes
	 * @param params
	 * @return
	 */
	public static createFlawSearchResultReport2(def flawRes,def params) {
		println "flawRes="+flawRes
		println "reportutil params="+params
		WorkbookSettings workbookSettings = new WorkbookSettings()
		workbookSettings.locale = Locale.default

		def file = File.createTempFile('myExcelDocument', '.xls')
		file.deleteOnExit()

		WritableWorkbook workbook = Workbook.createWorkbook(file, workbookSettings)

		WritableFont font = new WritableFont(WritableFont.ARIAL, 12)
		WritableCellFormat format = new WritableCellFormat(font)

		def row = 0
		def columnNum = 0
		WritableSheet sheet = workbook.createSheet('漏洞信息', 0)
		if(params.numberChek){
			sheet.addCell(new Label(columnNum++, row, "漏洞编号", format))
		}
		if(params.titleChek){
			sheet.addCell(new Label(columnNum++, row, "漏洞标题", format))
		}
		if(params.descriptionChek){
			sheet.addCell(new Label(columnNum++, row, "漏洞描述", format))
		}
		if(params.formalWayChek){
			sheet.addCell(new Label(columnNum++, row, "正式解决方案", format))
		}
		if(params.tempWayChek){
			sheet.addCell(new Label(columnNum++, row, "临时解决方案", format))
		}
		if(params.isFirstChek){
			sheet.addCell(new Label(columnNum++, row, "是否首次公开", format))
		}
		if(params.isZeroChek){
			sheet.addCell(new Label(columnNum++, row, "是否零日漏洞", format))
		}
		if(params.dateCreatedChek){
			sheet.addCell(new Label(columnNum++, row, "录入时间", format))
		}
		if(params.submitTimeChek){
			sheet.addCell(new Label(columnNum++, row, "报送时间", format))
		}
		if(params.storageTimeChek){
			sheet.addCell(new Label(columnNum++, row, "归档时间", format))
		}
		if(params.openTimeChek){
			sheet.addCell(new Label(columnNum++, row, "公开时间", format))
		}
		if(params.statusChek){
			sheet.addCell(new Label(columnNum++, row, "当前状态", format))
		}
		if(params.enableChek){
			sheet.addCell(new Label(columnNum++, row, "前台是否可见", format))
		}
		if(params.causeIdChek){
			sheet.addCell(new Label(columnNum++, row, "漏洞产生原因", format))
		}
		if(params.threadIdChek){
			sheet.addCell(new Label(columnNum++, row, "漏洞引发的威胁", format))
		}
		if(params.serverityIdChek){
			sheet.addCell(new Label(columnNum++, row, "漏洞严重程度", format))
		}
		if(params.positionIdChek){
			sheet.addCell(new Label(columnNum++, row, "漏洞利用的攻击位置", format))
		}
		if(params.softStyleIdChek){
			sheet.addCell(new Label(columnNum++, row, "漏洞影响对象类型", format))
		}
		if(params.isuChek){
			sheet.addCell(new Label(columnNum++, row, "是否前台上报", format))
		}
		if(params.userChek){
			sheet.addCell(new Label(columnNum++, row, "报送人", format))
		}
		if(params.isHotChek){
			sheet.addCell(new Label(columnNum++, row, "是否是热点漏洞", format))
		}
		if(params.isOriginalChek){
			sheet.addCell(new Label(columnNum++, row, "是否原创", format))
		}
		if(params.discovererNameChek){
			sheet.addCell(new Label(columnNum++, row, "发现者姓名", format))
		}
		if(params.isvChek){
			sheet.addCell(new Label(columnNum++, row, "是否验证 ", format))
		}
		if(params.exploitTypeChek){
			sheet.addCell(new Label(columnNum++, row, "验证类型 ", format))
		}
		if(params.exploitNameChek){
			sheet.addCell(new Label(columnNum++, row, "验证名称 ", format))
		}
		if(params.exploitUserChek){
			sheet.addCell(new Label(columnNum++, row, "前台验证用户 ", format))
		}
		if(params.exploitConceptChek){
			sheet.addCell(new Label(columnNum++, row, "验证原理 ", format))
		}
		if(params.exploitPocChek){
			sheet.addCell(new Label(columnNum++, row, "验证poc", format))
		}
		if(params.exploitSuggChek){
			sheet.addCell(new Label(columnNum++, row, "验证建议", format))
		}
		if(params.exploitTimeChek){
			sheet.addCell(new Label(columnNum++, row, "验证日期", format))
		}
		if(params.exploitStatusChek){
			sheet.addCell(new Label(columnNum++, row, "验证状态", format))
		}
		if(params.exploitContentChek){
			sheet.addCell(new Label(columnNum++, row, "验证审核意见", format))
		}
		if(params.exploitRefChek){
			sheet.addCell(new Label(columnNum++, row, "验证信息参考链接", format))
		}
		if(params.ivpChek){
			sheet.addCell(new Label(columnNum++, row, "是否处置", format))
		}
		if(params.patchIdChek){
			sheet.addCell(new Label(columnNum++, row, "补丁编号", format))
		}
		if(params.patchNameChek){
			sheet.addCell(new Label(columnNum++, row, "补丁名称", format))
		}
		if(params.patchValidChek){
			sheet.addCell(new Label(columnNum++, row, "补丁信息", format))
		}
		if(params.patchUserChek){
			sheet.addCell(new Label(columnNum++, row, "补丁贡献者", format))
		}
		if(params.patchDesChek){
			sheet.addCell(new Label(columnNum++, row, "补丁描述", format))
		}
		if(params.patchFunChek){
			sheet.addCell(new Label(columnNum++, row, "补丁验证原理", format))
		}
		if(params.patchUrlChek){
			sheet.addCell(new Label(columnNum++, row, "补丁链接", format))
		}
		if(params.patchSourceChek){
			sheet.addCell(new Label(columnNum++, row, "补丁来源", format))
		}
		if(params.patchStatusChek){
			sheet.addCell(new Label(columnNum++, row, "补丁状态", format))
		}
		if(params.referTypeChek){
			sheet.addCell(new Label(columnNum++, row, "外部引用", format))
		}
		if(params.reflectProductChek){
			sheet.addCell(new Label(columnNum++, row, "影响产品", format))
		}
		if(params.scoreChek){
			sheet.addCell(new Label(columnNum++, row, "评分(基本度量/时间度量/环境度量)", format))
		}
		if(params.scoreDetailChek){
			sheet.addCell(new Label(columnNum++, row, "攻击途径", format))
			sheet.addCell(new Label(columnNum++, row, "攻击复杂度", format))
			sheet.addCell(new Label(columnNum++, row, "认证", format))
			sheet.addCell(new Label(columnNum++, row, "机密性", format))
			sheet.addCell(new Label(columnNum++, row, "完整性", format))
			sheet.addCell(new Label(columnNum++, row, "可用性", format))
		}
		if(params.hangyeChek){
			sheet.addCell(new Label(columnNum++, row, "行业", format))
		}
		if(params.referenceLinkChek){
			sheet.addCell(new Label(columnNum++, row, "引用链接", format))
		}
		if(params.exmaineContentChek){
			sheet.addCell(new Label(columnNum++, row, "审核意见", format))
		}
		row++
		flawRes.each { flaw ->
			columnNum = 0
			if(params.numberChek){
				def numberStr = flaw.status==9?(flaw.number):(flaw.tempNumber?flaw.tempNumber:flaw.number)
				sheet.addCell(new Label(columnNum++, row, numberStr, format))//漏洞编号
			}
			if(params.titleChek){
				sheet.addCell(new Label(columnNum++, row, flaw.title, format))//漏洞标题
			}
			if(params.descriptionChek){
				sheet.addCell(new Label(columnNum++, row, flaw.description, format))//漏洞描述
			}
			if(params.formalWayChek){
				sheet.addCell(new Label(columnNum++, row, flaw.formalWay, format))//正式解决方案
			}
			if(params.tempWayChek){
				sheet.addCell(new Label(columnNum++, row, flaw.tempWay, format))//临时解决方案
			}
			if(params.isFirstChek){
				sheet.addCell(new Label(columnNum++, row, flaw.isFirst, format))//是否首次公开
			}
			if(params.isZeroChek){
				sheet.addCell(new Label(columnNum++, row, flaw.isZero, format))//是否零日漏洞
			}
			if(params.dateCreatedChek){
				sheet.addCell(new Label(columnNum++, row, flaw.dateCreated, format))//发现时间
			}
			if(params.submitTimeChek){
				sheet.addCell(new Label(columnNum++, row, flaw.submitTime, format))//报送时间
			}
			if(params.storageTimeChek){
				sheet.addCell(new Label(columnNum++, row, flaw.storageTime, format))//归档时间
			}
			if(params.openTimeChek){
				sheet.addCell(new Label(columnNum++, row, flaw.openTime, format))//公开时间
			}
			if(params.statusChek){
				sheet.addCell(new Label(columnNum++, row, flaw.statusStr, format))//当前状态
			}
			if(params.enableChek){
				sheet.addCell(new Label(columnNum++, row, flaw.enable, format))//前台是否可见
			}
			if(params.causeIdChek){
				sheet.addCell(new Label(columnNum++, row, flaw.cause, format))//漏洞产生原因
			}
			if(params.threadIdChek){
				sheet.addCell(new Label(columnNum++, row, flaw.thread,format))//漏洞引发的威胁
			}
			if(params.serverityIdChek){
				sheet.addCell(new Label(columnNum++, row, flaw.serverity, format))//漏洞严重程度
			}
			if(params.positionIdChek){
				sheet.addCell(new Label(columnNum++, row, flaw.position, format))//漏洞利用的攻击位置
			}
			if(params.softStyleIdChek){
				sheet.addCell(new Label(columnNum++, row, flaw.softStyle, format))//漏洞影响对象类型
			}
			if(params.isuChek){
				sheet.addCell(new Label(columnNum++, row, flaw.isu, format))
			}
			if(params.userChek){
				sheet.addCell(new Label(columnNum++, row, flaw.creatorName, format))
			}
			if(params.isHotChek){
				sheet.addCell(new Label(columnNum++, row, flaw.isHot, format))//是否是热点漏洞
			}
			if(params.isOriginalChek){
				sheet.addCell(new Label(columnNum++, row, flaw.isOriginal, format))//是否原创
			}
			if(params.discovererNameChek){
				sheet.addCell(new Label(columnNum++, row, flaw.discovererName, format))//发现者姓名
			}
			if(params.isvChek){
				sheet.addCell(new Label(columnNum++, row, flaw.isv, format))//是否验证
			}
			if(params.exploitTypeChek){
				sheet.addCell(new Label(columnNum++, row, flaw.exploitType, format))//验证类型
			}
			if(params.exploitNameChek){
				sheet.addCell(new Label(columnNum++, row, flaw.exploitName, format))//验证名称
			}
			if(params.exploitUserChek){
				sheet.addCell(new Label(columnNum++, row, flaw.exploitUserName, format))//前台验证用户
			}
			if(params.exploitConceptChek){
				sheet.addCell(new Label(columnNum++, row, flaw.exploitConcept, format))//验证原理
			}
			if(params.exploitPocChek){
				sheet.addCell(new Label(columnNum++, row, flaw.exploitPoc, format))//验证poc
			}
			if(params.exploitSuggChek){
				sheet.addCell(new Label(columnNum++, row, flaw.exploitSuggestion, format))//验证建议
			}
			if(params.exploitTimeChek){
				sheet.addCell(new Label(columnNum++, row, flaw.exploitTime, format))//验证日期
			}
			if(params.exploitStatusChek){
				sheet.addCell(new Label(columnNum++, row, flaw.exploitStatus, format))//验证状态
			}
			if(params.exploitContentChek){
				sheet.addCell(new Label(columnNum++, row, flaw.exploitContent, format))//审核意见
			}
			if(params.exploitRefChek){
				sheet.addCell(new Label(columnNum++, row, flaw.exploitRef, format))//验证信息参考链接
			}
			if(params.ivpChek){
				sheet.addCell(new Label(columnNum++, row, flaw.ivp, format))//是否处置
			}
			if(params.patchIdChek){
				sheet.addCell(new Label(columnNum++, row, flaw.patchId, format)) //补丁编号
			}
			if(params.patchNameChek){
				sheet.addCell(new Label(columnNum++, row, flaw.patchName, format))//补丁名称
			}
			if(params.patchValidChek){
				sheet.addCell(new Label(columnNum++, row, flaw.patchValid, format)) //补丁信息
			}
			if(params.patchUserChek){
				sheet.addCell(new Label(columnNum++, row, flaw.patchUserName, format)) //补丁贡献者
			}
			if(params.patchDesChek){
				sheet.addCell(new Label(columnNum++, row, flaw.patchDesc, format)) //补丁描述
			}
			if(params.patchFunChek){
				sheet.addCell(new Label(columnNum++, row, flaw.patchFunction, format))//补丁验证原理
			}
			if(params.patchUrlChek){
				sheet.addCell(new Label(columnNum++, row, flaw.patchUrl, format))//补丁链接
			}
			if(params.patchSourceChek){
				sheet.addCell(new Label(columnNum++, row, flaw.patchSource, format)) //补丁来源
			}
			if(params.patchStatusChek){
				sheet.addCell(new Label(columnNum++, row, flaw.patchStatus, format)) //补丁状态
			}
			if(params.referTypeChek){
				sheet.addCell(new Label(columnNum++, row, flaw.referenceStr, format))
			}
			if(params.reflectProductChek){
				sheet.addCell(new Label(columnNum++, row, flaw.reflectProductStr, format))
			}
			if(params.scoreChek){
				sheet.addCell(new Label(columnNum++, row, new String(flaw.scoreStr), format))
			}
			if(params.scoreDetailChek){
				println "scoreDetailChek"
				//评分详情
				sheet.addCell(new Label(columnNum++, row, flaw.accessVectorName, format))
				sheet.addCell(new Label(columnNum++, row, flaw.accessComplexityName, format))
				sheet.addCell(new Label(columnNum++, row, flaw.authenticationName, format))
				sheet.addCell(new Label(columnNum++, row, flaw.confidentialityImpactName, format))
				sheet.addCell(new Label(columnNum++, row, flaw.integrityImpactName, format))
				sheet.addCell(new Label(columnNum++, row, flaw.availabilityImpactName, format))
			}
			if(params.hangyeChek){
				sheet.addCell(new Label(columnNum++, row, flaw.hangye, format))
			}
			if(params.referenceLinkChek){
				sheet.addCell(new Label(columnNum++, row, flaw.referenceLink, format))
			}
			if(params.exmaineContentChek){
				sheet.addCell(new Label(columnNum++, row, flaw.exmaineContent, format))
			}
			println "reportUtil AAAAAAAAAAAAAAAAAAAAAAAAAAA"
			row++
		}
		workbook.write()
		workbook.close()
		return file
	}
	
	
	public static createMonthDataExcel(def flawTypeStr,def serverityStr,def lineStr,def patchInfoStr,def patchTrendStr,def userFlawStr){
		WorkbookSettings workbookSettings = new WorkbookSettings()
		workbookSettings.locale = Locale.default
		SimpleDateFormat sdf =  new SimpleDateFormat("yyyy-MM-dd");

		def file = File.createTempFile('myExcelDocument', '.xls')
		file.deleteOnExit()

		WritableWorkbook workbook = Workbook.createWorkbook(file, workbookSettings)

		WritableFont font1 = new WritableFont(WritableFont.ARIAL, 10)
		WritableCellFormat format = new WritableCellFormat(font1)

		def sheet0row = 0
		def sheet1row = 0
		def sheet2row = 0
		def sheet3row = 0
		def sheet4row = 0
		WritableSheet sheet0 = workbook.createSheet('漏洞类型', 0)
		WritableSheet sheet1 = workbook.createSheet('漏洞发布趋势', 1)
		WritableSheet sheet2 = workbook.createSheet('补丁按漏洞等级分布', 2)
		WritableSheet sheet3 = workbook.createSheet('补丁发布趋势', 3)
		WritableSheet sheet4 = workbook.createSheet('成员单位上报统计', 4)
		WritableSheet sheet5 = workbook.createSheet('漏洞按类型分布发布趋势', 5)
		
		sheet0.addCell(new Label(3, sheet0row,"漏洞类型" , format))
		sheet0.addCell(new Label(4, sheet0row, "每类总数", format))
		
		String[] flawTypeStrArr = flawTypeStr.split(";")
		sheet0row++
		for(String str : flawTypeStrArr){
			String[] arr = str.split("#")
			sheet0.addCell(new Label(3, sheet0row, arr[0] , format))
			sheet0.addCell(new Number(4,sheet0row,Double.parseDouble(arr[1])))
			sheet0row++
		}
		sheet0row += 10
		
		sheet0.addCell(new Label(3, sheet0row, "漏洞等级" , format))
		sheet0.addCell(new Label(4, sheet0row, "数量", format))
		String[] serverityStrArr = serverityStr.split(";")
		sheet0row++
		for(String str : serverityStrArr){
			String[] arr = str.split("#")
			sheet0.addCell(new Label(3, sheet0row, arr[0] , format))
			sheet0.addCell(new Number(4,sheet0row,Double.parseDouble(arr[1])))
			sheet0row++
		}
		
		/*File image = new File("f:\\x.png")
		WritableImage wimage = new WritableImage(7,10,5,10,image);
		sheet0.addImage(wimage)*/
		
		sheet1.addCell(new Label(0, sheet1row, "日期" , format))
		sheet1.addCell(new Label(1, sheet1row, "漏洞数", format))
		sheet1.addCell(new Label(2, sheet1row, "高危漏洞", format))
		sheet1row++
		String[] dayFlawArr = lineStr.split(";")
		for(String str:dayFlawArr){
			String[] arr = str.split("#")
			sheet1.addCell(new Label(0, sheet1row, arr[0] , format))
			sheet1.addCell(new Number(1,sheet1row,Double.parseDouble(arr[2])))
			sheet1.addCell(new Number(2,sheet1row,Double.parseDouble(arr[1])))
			sheet1row++
		}
		
		sheet2.addCell(new Label(0, sheet2row, "分类" , format))
		sheet2.addCell(new Label(1, sheet2row, "数量", format))
		sheet2row++
		String[] patchInfoArr = patchInfoStr.split(";")
		for(String str:patchInfoArr){
			String[] arr = str.split("#")
			sheet2.addCell(new Label(0, sheet2row, arr[0], format))
			sheet2.addCell(new Number(1,sheet2row,Double.parseDouble(arr[1])))
			sheet2row++
		}
		
		
		sheet3.addCell(new Label(0, sheet3row, "日期" , format))
		sheet3.addCell(new Label(1, sheet3row, "每天漏洞数目", format))
		sheet3.addCell(new Label(2, sheet3row, "每天修补数目", format))
		sheet3row++
		String[] patchTrendArr = patchTrendStr.split(";")
		for(String str:patchTrendArr){
			String[] arr = str.split("#")
			sheet3.addCell(new Label(0, sheet3row, arr[0], format))
			sheet3.addCell(new Number(1,sheet3row,Double.parseDouble(arr[1])))
			sheet3.addCell(new Number(2,sheet3row,Double.parseDouble(arr[2])))
			sheet3row++
		}
		
		sheet4.addCell(new Label(0, sheet4row, "成员单位" , format))
		sheet4.addCell(new Label(1, sheet4row, "漏洞上报总数", format))
		sheet4row++
		String[] userFlawArr = userFlawStr.split(";")
		for(int i=0;i<userFlawArr.length;i++){
			def str = userFlawArr[i]
			String[] arr = str.split("#")
			sheet4.addCell(new Label(0, sheet4row, arr[0], format))
			if(i==userFlawArr.length-1){
				sheet4.addCell(new Label(1, sheet4row,arr[1]+"（去重）", format))
			}else{
				sheet4.addCell(new Number(1,sheet4row,Double.parseDouble(arr[1])))
			}
			sheet4row++
		}
		
		workbook.write()
		workbook.close()
		return file
	}
	
	public static createSecretExcel(def list){
		WorkbookSettings workbookSettings = new WorkbookSettings()
		workbookSettings.locale = Locale.default
		SimpleDateFormat sdf =  new SimpleDateFormat("yyyy-MM-dd");

		def file = File.createTempFile('myExcelDocument', '.xlsx')
		file.deleteOnExit()

		WritableWorkbook workbook = Workbook.createWorkbook(file, workbookSettings)

		WritableFont font1 = new WritableFont(WritableFont.ARIAL, 12)
		WritableCellFormat format = new WritableCellFormat(font1)

		def row = 0
		WritableSheet sheet = workbook.createSheet('CNVD同步漏洞', 0)
		sheet.addCell(new Label(0, row, "漏洞标题" , format))
		sheet.addCell(new Label(1, row, "漏洞描述" , format))
		sheet.addCell(new Label(2, row, "正式解决方案", format))
		sheet.addCell(new Label(3, row, "CVE编号", format))
		sheet.addCell(new Label(4, row, "CNVD编号", format))
		sheet.addCell(new Label(5, row, "CNVVD编号", format))
		sheet.addCell(new Label(6, row, "OSVDB编号", format))
		sheet.addCell(new Label(7, row, "危险等级", format))
		sheet.addCell(new Label(8, row, "发现时间", format))
		sheet.addCell(new Label(9, row, "发现者姓名", format))
		sheet.addCell(new Label(10, row, "漏洞产生原因", format))
		sheet.addCell(new Label(11, row, "漏洞引发的威胁", format))
		sheet.addCell(new Label(12, row, "漏洞利用的攻击位置", format))
		sheet.addCell(new Label(13, row, "漏洞影响对象领域", format))
		sheet.addCell(new Label(14, row, "漏洞影响对象类型", format))
		sheet.addCell(new Label(15, row, "漏洞来源", format))
		sheet.addCell(new Label(16, row, "所属厂商", format))
		sheet.addCell(new Label(17, row, "验证名称", format))
		sheet.addCell(new Label(18, row, "验证原理", format))
		sheet.addCell(new Label(19, row, "验证参考链接", format))
		sheet.addCell(new Label(20, row, "验证POC", format))
		sheet.addCell(new Label(21, row, "验证建议", format))
		sheet.addCell(new Label(22, row, "补丁名称", format))
		sheet.addCell(new Label(23, row, "补丁原理", format))
		sheet.addCell(new Label(24, row, "补丁链接", format))
		sheet.addCell(new Label(25, row, "补丁描述", format))
		sheet.addCell(new Label(26, row, "影响产品", format))
		
		row++
		list.each {
			def flaw = it
			def relationFlawNum = ""
			if(it.parentFlaw){
				//漏洞有关联漏洞
				flaw = it.parentFlaw
				if(flaw.status == 9){
					relationFlawNum = flaw.number
				}else{
					relationFlawNum = flaw.tempNumber?flaw.tempNumber:flaw.number
				}
			}else{
				relationFlawNum = "无"
			}
			def numberStr = it.number
			sheet.addCell(new Label(0, row, flaw.title, format))
			sheet.addCell(new Label(1, row, flaw.detailedInfo?.description, format))//关联漏洞编号
			sheet.addCell(new Label(2, row, flaw.detailedInfo?.formalWay, format))
			
			def referenceInfoList = ReferenceInfo.findAllByFlawAndReferenceType(flaw,ReferenceType.get(1))
			String cveStr = "";
			if(referenceInfoList){
				for(def ref : referenceInfoList){
					cveStr += ref.referenceNumber+","
				}
				cveStr = cveStr.substring(0,cveStr.length()-1)
			}
			sheet.addCell(new Label(3, row, cveStr, format))
			sheet.addCell(new Label(4, row, numberStr, format)) //CNVD编号
			sheet.addCell(new Label(5, row, "", format))  //CNNVD编号
			
			
			//TODO 查询osvdb编号
			String osvdbNumber = "";
			def osvdbReferList = ReferenceInfo.executeQuery(" from ReferenceInfo where referenceNumber like ? and referenceType.id=? and flaw = ?",['%osvdb%',3L,flaw])
			if(osvdbReferList){
				osvdbReferList.each{
					osvdbNumber += it.referenceNumber+";"
				}
				osvdbNumber = osvdbNumber.substring(0,osvdbNumber.length()-1)
			}
			sheet.addCell(new Label(6, row, osvdbNumber, format))  //OSVDB编号
			
			String levelStr = "";
			if(flaw.serverityId == 18){
				levelStr = "高危";
			}else if(flaw.serverityId == 19){
				levelStr = "中危";
			}else if(flaw.serverityId == 20){
				levelStr = "低危";
			}else{
				levelStr = "未评级";
			}
			sheet.addCell(new Label(7, row, levelStr, format))  //危险等级
			sheet.addCell(new Label(8, row, sdf.format(flaw.foundTime), format))  //发现时间
			sheet.addCell(new Label(9, row, flaw.discovererName, format))  //发现者姓名
			
			String causeStr = "";
			if(flaw.causeId == 1){
				causeStr="输入验证错误";
			}else if(flaw.causeId == 2){
				causeStr="访问验证错误";
			}else if(flaw.causeId == 3){
				causeStr="意外情况处理错误";
			}else if(flaw.causeId == 4){
				causeStr="边界条件错误";
			}else if(flaw.causeId == 5){
				causeStr="配置错误";
			}else if(flaw.causeId == 6){
				causeStr="竞争条件";
			}else if(flaw.causeId == 7){
				causeStr="环境错误";
			}else if(flaw.causeId == 8){
				causeStr="设计错误";
			}else if(flaw.causeId == 9){
				causeStr="其他错误";
			}else if(flaw.causeId == 10){
				causeStr="未知错误";
			}
			sheet.addCell(new Label(10, row, causeStr, format))
			String threadStr = "";
			if(flaw.threadId == 11){
				threadStr="管理员访问权限获取";
			}else if(flaw.threadId == 12){
				threadStr="普通用户访问权限获取";
			}else if(flaw.threadId == 13){
				threadStr="未授权的信息泄露";
			}else if(flaw.threadId == 14){
				threadStr="未授权的信息修改";
			}else if(flaw.threadId == 15){
				threadStr="拒绝服务";
			}else if(flaw.threadId == 16){
				threadStr="其它";
			}else if(flaw.threadId == 17){
				threadStr="未知";
			}
			sheet.addCell(new Label(11, row, threadStr, format))
			
			def positionStr = ""
			if(flaw.positionId == 21){
				positionStr = "远程"
			}else if(flaw.positionId == 22){
				positionStr = "本地"
			}else if(flaw.positionId == 23){
				positionStr = "其他"
			}
			sheet.addCell(new Label(12, row, positionStr, format))
			sheet.addCell(new Label(13, row, "", format))
			
			def softStyleStr = ""
			if(flaw.softStyleId == 27){
				softStyleStr = "操作系统漏洞"
			}else if(flaw.softStyleId == 28){
				softStyleStr = "应用程序漏洞"
			}else if(flaw.softStyleId == 29){
				softStyleStr = "WEB应用漏洞"
			}else if(flaw.softStyleId == 30){
				softStyleStr = "数据库漏洞"
			}else if(flaw.softStyleId == 31){
				softStyleStr = "网络设备漏洞"
			}else if(flaw.softStyleId == 32){
				softStyleStr = "安全产品漏洞"
			}
			sheet.addCell(new Label(14, row, softStyleStr, format))
			sheet.addCell(new Label(15, row, "", format))
			
			sheet.addCell(new Label(16, row, flaw.manufacturer?flaw.manufacturer.name:"", format))
			
			def exploit = Exploit.findByFlawAndEnable(flaw,1)
			if(exploit){
				sheet.addCell(new Label(17, row, exploit.exploitName, format))
				sheet.addCell(new Label(18, row, exploit.concept, format))
				sheet.addCell(new Label(19, row, exploit.referenceLink, format))
				sheet.addCell(new Label(20, row, exploit.poc, format))
				sheet.addCell(new Label(21, row, exploit.suggestion, format))
			}else{
				sheet.addCell(new Label(17, row, "", format))
				sheet.addCell(new Label(18, row, "", format))
				sheet.addCell(new Label(19, row, "", format))
				sheet.addCell(new Label(20, row, "", format))
				sheet.addCell(new Label(21, row, "", format))
			}
			
			def patchInfo = PatchInfo.findByFlawAndEnable(flaw,1)
			if(patchInfo){
				sheet.addCell(new Label(22, row, patchInfo.patchName, format))
				sheet.addCell(new Label(23, row, patchInfo.function, format))
				sheet.addCell(new Label(24, row, patchInfo.patchUrl, format))
				sheet.addCell(new Label(25, row, patchInfo.patchDescription, format))
			}else{
				sheet.addCell(new Label(22, row, "", format))
				sheet.addCell(new Label(23, row, "", format))
				sheet.addCell(new Label(24, row, "", format))
				sheet.addCell(new Label(25, row, "", format))
			}
			
			def reflectProductStr = ""
			def flawProductList = FlawProduct.findAllByFlaw(flaw)
			if(flawProductList){
				flawProductList.each{
					reflectProductStr += it.product.manufacturer.name + "&" + it.product.productCategory.name + "&" +it.product.edition + ";"
				}
				reflectProductStr = reflectProductStr.substring(0, reflectProductStr.length() - 1)
			}
			println "reflectProductStr="+reflectProductStr
			
			sheet.addCell(new Label(26, row, reflectProductStr, format))
			
			row++
		}
		workbook.write()
		workbook.close()
		return file
	}
}
