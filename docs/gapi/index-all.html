<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">

<html><head>
<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
<!-- Generated by groovydoc (1.6.7) on  -->
<title>Index (jcaptcha)</title>
<meta name="date" content="2010-02-05">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link href="groovy.ico" type="image/x-icon" rel="shortcut icon">
<link href="groovy.ico" type="image/x-icon" rel="icon">
<script type="text/javascript">
function windowTitle()
{
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Index (jcaptcha)";
    }
}
</script>
<noscript>
</noscript>

</head><body onload="windowTitle();" bgcolor="white">
<hr>

<!-- ========= START OF TOP NAVBAR ======= -->
<a name="navbar_top"><!-- --></a>
<table summary="" width="100%" border="0" cellpadding="1" cellspacing="0">
<tbody><tr>
<td colspan="2" class="NavBarCell1" bgcolor="#eeeeff">
<a name="navbar_top_firstrow"><!-- --></a>
<table summary="" border="0" cellpadding="0" cellspacing="3">
  <tbody><tr valign="top" align="center">
  <td class="NavBarCell1" bgcolor="#eeeeff">    <a href="overview-summary.html"><font class="NavBarFont1"><b>Overview</b></font></a>&nbsp;</td>
  <td class="NavBarCell1" bgcolor="#eeeeff">    <font class="NavBarFont1">Package</font>&nbsp;</td>
  <td class="NavBarCell1" bgcolor="#eeeeff">    <font class="NavBarFont1">Class</font>&nbsp;</td>
  <!--
  <td class="NavBarCell1" bgcolor="#eeeeff">    <a href="overview-tree.html"><font class="NavBarFont1"><b>Tree</b></font></a>&nbsp;</td>
  -->
  <td class="NavBarCell1" bgcolor="#eeeeff">    <a href="deprecated-list.html"><font class="NavBarFont1"><b>Deprecated</b></font></a>&nbsp;</td>
  <td class="NavBarCell1Rev" bgcolor="#ffffff"> &nbsp;<font class="NavBarFont1Rev"><b>Index</b></font>&nbsp;</td>
  <td class="NavBarCell1" bgcolor="#eeeeff">    <a href="help-doc.html"><font class="NavBarFont1"><b>Help</b></font></a>&nbsp;</td>
  </tr>
</tbody></table>
</td>
<td rowspan="3" valign="top" align="right"><em>
    <b>Groovy Documentation</b>
</em>
</td>
</tr>

<tr>
<td class="NavBarCell2" bgcolor="white"><font size="-2">
&nbsp;PREV&nbsp;
&nbsp;NEXT</font></td>
<td class="NavBarCell2" bgcolor="white"><font size="-2">
  <a href="index.html?index-all.html" target="_top"><b>FRAMES</b></a>  &nbsp;
&nbsp;<a href="index-all.html" target="_top"><b>NO FRAMES</b></a>  &nbsp;
&nbsp;<script type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="allclasses-frame.html"><B>All Classes</B></A>');
  }
  //-->
</script>
<noscript>
  <A HREF="allclasses-frame.html"><B>All Classes</B></A>
</noscript>

</font></td>
</tr>
</tbody></table>
<!-- ========= END OF TOP NAVBAR ========= -->


<a href="#_A_">A</a>

<a href="#_B_">B</a>

<a href="#_C_">C</a>

<a href="#_D_">D</a>

<a href="#_E_">E</a>

<a href="#_F_">F</a>

<a href="#_G_">G</a>

<a href="#_H_">H</a>

<a href="#_I_">I</a>

<a href="#_J_">J</a>

<a href="#_K_">K</a>

<a href="#_L_">L</a>

<a href="#_M_">M</a>

<a href="#_N_">N</a>

<a href="#_O_">O</a>

<a href="#_P_">P</a>

<a href="#_Q_">Q</a>

<a href="#_R_">R</a>

<a href="#_S_">S</a>

<a href="#_T_">T</a>

<a href="#_U_">U</a>

<a href="#_V_">V</a>

<a href="#_W_">W</a>

<a href="#_X_">X</a>

<a href="#_Y_">Y</a>

<a href="#_Z_">Z</a>

<a href="#___">_</a>




<hr>
<a name="_A_"><!-- --></a><h2>
<b>A</b></h2>

<dl>

<dt><a href="grails-app/controllers/grails/plugin/jcaptcha/JcaptchaController.html#afterInterceptor" title="Property in JcaptchaController"><b>afterInterceptor</b></a> - Property in <a href="grails-app/controllers/grails/plugin/jcaptcha/JcaptchaController.html">JcaptchaController</a>
</dt><dd></dd>
<dt><a href="DefaultPackage/JcaptchaGrailsPlugin.html#author" title="Property in JcaptchaGrailsPlugin"><b>author</b></a> - Property in <a href="DefaultPackage/JcaptchaGrailsPlugin.html">JcaptchaGrailsPlugin</a>
</dt><dd></dd>
<dt><a href="DefaultPackage/JcaptchaGrailsPlugin.html#authorEmail" title="Property in JcaptchaGrailsPlugin"><b>authorEmail</b></a> - Property in <a href="DefaultPackage/JcaptchaGrailsPlugin.html">JcaptchaGrailsPlugin</a>
</dt><dd></dd>
</dl>

    

<hr>
<a name="_B_"><!-- --></a><h2>
<b>B</b></h2>

<dl>

<dt><a href="grails-app/controllers/grails/plugin/jcaptcha/JcaptchaController.html#beforeInterceptor" title="Property in JcaptchaController"><b>beforeInterceptor</b></a> - Property in <a href="grails-app/controllers/grails/plugin/jcaptcha/JcaptchaController.html">JcaptchaController</a>
</dt><dd></dd>
</dl>

    

<hr>
<a name="_C_"><!-- --></a><h2>
<b>C</b></h2>

<dl>

<dt><a href="grails-app/controllers/grails/plugin/jcaptcha/JcaptchaController.html#captcha" title="Property in JcaptchaController"><b>captcha</b></a> - Property in <a href="grails-app/controllers/grails/plugin/jcaptcha/JcaptchaController.html">JcaptchaController</a>
</dt><dd></dd>
<dt><a href="grails-app/controllers/grails/plugin/jcaptcha/JcaptchaController.html#captchaName" title="Property in JcaptchaController"><b>captchaName</b></a> - Property in <a href="grails-app/controllers/grails/plugin/jcaptcha/JcaptchaController.html">JcaptchaController</a>
</dt><dd></dd>
<dt><a href="grails-app/controllers/grails/plugin/jcaptcha/JcaptchaController.html#challenge" title="Property in JcaptchaController"><b>challenge</b></a> - Property in <a href="grails-app/controllers/grails/plugin/jcaptcha/JcaptchaController.html">JcaptchaController</a>
</dt><dd></dd>
<dt><a href="grails-app/services/grails/plugin/jcaptcha/JcaptchaService.html#challengeAsJpeg(BufferedImage)" title="Method in JcaptchaService"><b>challengeAsJpeg</b>(BufferedImage)</b></a> - Method in <a href="grails-app/services/grails/plugin/jcaptcha/JcaptchaService.html">JcaptchaService</a>
</dt><dd> Utility routine to turn an image challenge into a JPEG stream.
 
 </dd>
<dt><a href="grails-app/services/grails/plugin/jcaptcha/JcaptchaService.html#challengeAsWav(AudioInputStream)" title="Method in JcaptchaService"><b>challengeAsWav</b>(AudioInputStream)</b></a> - Method in <a href="grails-app/services/grails/plugin/jcaptcha/JcaptchaService.html">JcaptchaService</a>
</dt><dd> Utility routine to turn a sound challenge into a WAV stream.
 
 </dd>
</dl>

    

<hr>
<a name="_D_"><!-- --></a><h2>
<b>D</b></h2>

<dl>

<dt><a href="grails-app/controllers/grails/plugin/jcaptcha/JcaptchaController.html#data" title="Property in JcaptchaController"><b>data</b></a> - Property in <a href="grails-app/controllers/grails/plugin/jcaptcha/JcaptchaController.html">JcaptchaController</a>
</dt><dd></dd>
<dt><a href="DefaultPackage/JcaptchaGrailsPlugin.html#dependsOn" title="Property in JcaptchaGrailsPlugin"><b>dependsOn</b></a> - Property in <a href="DefaultPackage/JcaptchaGrailsPlugin.html">JcaptchaGrailsPlugin</a>
</dt><dd></dd>
<dt><a href="DefaultPackage/JcaptchaGrailsPlugin.html#description" title="Property in JcaptchaGrailsPlugin"><b>description</b></a> - Property in <a href="DefaultPackage/JcaptchaGrailsPlugin.html">JcaptchaGrailsPlugin</a>
</dt><dd></dd>
<dt><a href="DefaultPackage/JcaptchaGrailsPlugin.html#documentation" title="Property in JcaptchaGrailsPlugin"><b>documentation</b></a> - Property in <a href="DefaultPackage/JcaptchaGrailsPlugin.html">JcaptchaGrailsPlugin</a>
</dt><dd></dd>
<dt><a href="DefaultPackage/JcaptchaGrailsPlugin.html#doWithApplicationContext" title="Property in JcaptchaGrailsPlugin"><b>doWithApplicationContext</b></a> - Property in <a href="DefaultPackage/JcaptchaGrailsPlugin.html">JcaptchaGrailsPlugin</a>
</dt><dd></dd>
<dt><a href="DefaultPackage/JcaptchaGrailsPlugin.html#doWithDynamicMethods" title="Property in JcaptchaGrailsPlugin"><b>doWithDynamicMethods</b></a> - Property in <a href="DefaultPackage/JcaptchaGrailsPlugin.html">JcaptchaGrailsPlugin</a>
</dt><dd></dd>
<dt><a href="DefaultPackage/JcaptchaGrailsPlugin.html#doWithSpring" title="Property in JcaptchaGrailsPlugin"><b>doWithSpring</b></a> - Property in <a href="DefaultPackage/JcaptchaGrailsPlugin.html">JcaptchaGrailsPlugin</a>
</dt><dd></dd>
<dt><a href="DefaultPackage/JcaptchaGrailsPlugin.html#doWithWebDescriptor" title="Property in JcaptchaGrailsPlugin"><b>doWithWebDescriptor</b></a> - Property in <a href="DefaultPackage/JcaptchaGrailsPlugin.html">JcaptchaGrailsPlugin</a>
</dt><dd></dd>
</dl>

    

<hr>
<a name="_E_"><!-- --></a><h2>
<b>E</b></h2>

<dl>

</dl>

    

<hr>
<a name="_F_"><!-- --></a><h2>
<b>F</b></h2>

<dl>

</dl>

    

<hr>
<a name="_G_"><!-- --></a><h2>
<b>G</b></h2>

<dl>

<dt><a href="grails-app/services/grails/plugin/jcaptcha/JcaptchaService.html#getCaptchaService(String)" title="Method in JcaptchaService"><b>getCaptchaService</b>(String)</b></a> - Method in <a href="grails-app/services/grails/plugin/jcaptcha/JcaptchaService.html">JcaptchaService</a>
</dt><dd> Retrieves a captcha by name.
 
 </dd>
<dt><a href="grails-app/services/grails/plugin/jcaptcha/JcaptchaService.html#grailsApplication" title="Property in JcaptchaService"><b>grailsApplication</b></a> - Property in <a href="grails-app/services/grails/plugin/jcaptcha/JcaptchaService.html">JcaptchaService</a>
</dt><dd> Used to access the captchas defined as part of the app config.
	 </dd>
</dl>

    

<hr>
<a name="_H_"><!-- --></a><h2>
<b>H</b></h2>

<dl>

</dl>

    

<hr>
<a name="_I_"><!-- --></a><h2>
<b>I</b></h2>

<dl>

</dl>

    

<hr>
<a name="_J_"><!-- --></a><h2>
<b>J</b></h2>

<dl>

<dt><a href="grails-app/controllers/grails/plugin/jcaptcha/JcaptchaController.html" title="Class in grails-app.controllers.grails.plugin.jcaptcha"><b>JcaptchaController</b></a> - Class in <a href="./grails-app/controllers/grails/plugin/jcaptcha/package-summary.html">grails-app.controllers.grails.plugin.jcaptcha</a>
</dt><dd> Exposes actions that 'render' captcha challenges.
 
 </dd>
<dt><a href="grails-app/controllers/grails/plugin/jcaptcha/JcaptchaController.html#JcaptchaController()" title="Constructor in JcaptchaController"><b>JcaptchaController</b>()</b></a> - Constructor in <a href="grails-app/controllers/grails/plugin/jcaptcha/JcaptchaController.html">JcaptchaController</a>
</dt><dd></dd>
<dt><a href="DefaultPackage/JcaptchaGrailsPlugin.html" title="Class in DefaultPackage"><b>JcaptchaGrailsPlugin</b></a> - Class in <a href="./DefaultPackage/package-summary.html">DefaultPackage</a>
</dt><dd> Simplest. </dd>
<dt><a href="DefaultPackage/JcaptchaGrailsPlugin.html#JcaptchaGrailsPlugin()" title="Constructor in JcaptchaGrailsPlugin"><b>JcaptchaGrailsPlugin</b>()</b></a> - Constructor in <a href="DefaultPackage/JcaptchaGrailsPlugin.html">JcaptchaGrailsPlugin</a>
</dt><dd></dd>
<dt><a href="grails-app/services/grails/plugin/jcaptcha/JcaptchaService.html" title="Class in grails-app.services.grails.plugin.jcaptcha"><b>JcaptchaService</b></a> - Class in <a href="./grails-app/services/grails/plugin/jcaptcha/package-summary.html">grails-app.services.grails.plugin.jcaptcha</a>
</dt><dd> Provides access to the captchas as well as provides some util
 type methods to convert captchas to usable data.
 
 </dd>
<dt><a href="grails-app/controllers/grails/plugin/jcaptcha/JcaptchaController.html#jcaptchaService" title="Property in JcaptchaController"><b>jcaptchaService</b></a> - Property in <a href="grails-app/controllers/grails/plugin/jcaptcha/JcaptchaController.html">JcaptchaController</a>
</dt><dd></dd>
<dt><a href="grails-app/services/grails/plugin/jcaptcha/JcaptchaService.html#JcaptchaService()" title="Constructor in JcaptchaService"><b>JcaptchaService</b>()</b></a> - Constructor in <a href="grails-app/services/grails/plugin/jcaptcha/JcaptchaService.html">JcaptchaService</a>
</dt><dd></dd>
<dt><a href="grails-app/taglib/grails/plugin/jcaptcha/JcaptchaTagLib.html" title="Class in grails-app.taglib.grails.plugin.jcaptcha"><b>JcaptchaTagLib</b></a> - Class in <a href="./grails-app/taglib/grails/plugin/jcaptcha/package-summary.html">grails-app.taglib.grails.plugin.jcaptcha</a>
</dt><dd> Some convenience tags for 'displaying' captcha challenges. 
 
 </dd>
<dt><a href="grails-app/taglib/grails/plugin/jcaptcha/JcaptchaTagLib.html#JcaptchaTagLib()" title="Constructor in JcaptchaTagLib"><b>JcaptchaTagLib</b>()</b></a> - Constructor in <a href="grails-app/taglib/grails/plugin/jcaptcha/JcaptchaTagLib.html">JcaptchaTagLib</a>
</dt><dd></dd>
<dt><a href="grails-app/controllers/grails/plugin/jcaptcha/JcaptchaController.html#jpeg" title="Property in JcaptchaController"><b>jpeg</b></a> - Property in <a href="grails-app/controllers/grails/plugin/jcaptcha/JcaptchaController.html">JcaptchaController</a>
</dt><dd></dd>
<dt><a href="grails-app/taglib/grails/plugin/jcaptcha/JcaptchaTagLib.html#jpeg" title="Property in JcaptchaTagLib"><b>jpeg</b></a> - Property in <a href="grails-app/taglib/grails/plugin/jcaptcha/JcaptchaTagLib.html">JcaptchaTagLib</a>
</dt><dd> Insert an 'img' tag with a JPEG image challenge.
 
 </dd>
</dl>

    

<hr>
<a name="_K_"><!-- --></a><h2>
<b>K</b></h2>

<dl>

</dl>

    

<hr>
<a name="_L_"><!-- --></a><h2>
<b>L</b></h2>

<dl>

</dl>

    

<hr>
<a name="_M_"><!-- --></a><h2>
<b>M</b></h2>

<dl>

<dt><a href="grails-app/conf/UrlMappings.html#mappings" title="Property in UrlMappings"><b>mappings</b></a> - Property in <a href="grails-app/conf/UrlMappings.html">UrlMappings</a>
</dt><dd></dd>
</dl>

    

<hr>
<a name="_N_"><!-- --></a><h2>
<b>N</b></h2>

<dl>

<dt><a href="grails-app/taglib/grails/plugin/jcaptcha/JcaptchaTagLib.html#namespace" title="Property in JcaptchaTagLib"><b>namespace</b></a> - Property in <a href="grails-app/taglib/grails/plugin/jcaptcha/JcaptchaTagLib.html">JcaptchaTagLib</a>
</dt><dd> All tags are <jcaptcha:* />
	 </dd>
</dl>

    

<hr>
<a name="_O_"><!-- --></a><h2>
<b>O</b></h2>

<dl>

<dt><a href="DefaultPackage/JcaptchaGrailsPlugin.html#onApplicationChange" title="Property in JcaptchaGrailsPlugin"><b>onApplicationChange</b></a> - Property in <a href="DefaultPackage/JcaptchaGrailsPlugin.html">JcaptchaGrailsPlugin</a>
</dt><dd></dd>
<dt><a href="DefaultPackage/JcaptchaGrailsPlugin.html#onChange" title="Property in JcaptchaGrailsPlugin"><b>onChange</b></a> - Property in <a href="DefaultPackage/JcaptchaGrailsPlugin.html">JcaptchaGrailsPlugin</a>
</dt><dd></dd>
</dl>

    

<hr>
<a name="_P_"><!-- --></a><h2>
<b>P</b></h2>

<dl>

</dl>

    

<hr>
<a name="_Q_"><!-- --></a><h2>
<b>Q</b></h2>

<dl>

</dl>

    

<hr>
<a name="_R_"><!-- --></a><h2>
<b>R</b></h2>

<dl>

</dl>

    

<hr>
<a name="_S_"><!-- --></a><h2>
<b>S</b></h2>

<dl>

</dl>

    

<hr>
<a name="_T_"><!-- --></a><h2>
<b>T</b></h2>

<dl>

<dt><a href="DefaultPackage/JcaptchaGrailsPlugin.html#title" title="Property in JcaptchaGrailsPlugin"><b>title</b></a> - Property in <a href="DefaultPackage/JcaptchaGrailsPlugin.html">JcaptchaGrailsPlugin</a>
</dt><dd></dd>
</dl>

    

<hr>
<a name="_U_"><!-- --></a><h2>
<b>U</b></h2>

<dl>

<dt><a href="grails-app/conf/UrlMappings.html" title="Class in grails-app.conf"><b>UrlMappings</b></a> - Class in <a href="./grails-app/conf/package-summary.html">grails-app.conf</a>
</dt><dd></dd>
<dt><a href="grails-app/conf/UrlMappings.html#UrlMappings()" title="Constructor in UrlMappings"><b>UrlMappings</b>()</b></a> - Constructor in <a href="grails-app/conf/UrlMappings.html">UrlMappings</a>
</dt><dd></dd>
</dl>

    

<hr>
<a name="_V_"><!-- --></a><h2>
<b>V</b></h2>

<dl>

<dt><a href="grails-app/services/grails/plugin/jcaptcha/JcaptchaService.html#validateResponse(def, def, def)" title="Method in JcaptchaService"><b>validateResponse</b>(def, def, def)</b></a> - Method in <a href="grails-app/services/grails/plugin/jcaptcha/JcaptchaService.html">JcaptchaService</a>
</dt><dd> Used to verify the response to a challenge.
 
 </dd>
<dt><a href="DefaultPackage/JcaptchaGrailsPlugin.html#version" title="Property in JcaptchaGrailsPlugin"><b>version</b></a> - Property in <a href="DefaultPackage/JcaptchaGrailsPlugin.html">JcaptchaGrailsPlugin</a>
</dt><dd></dd>
</dl>

    

<hr>
<a name="_W_"><!-- --></a><h2>
<b>W</b></h2>

<dl>

<dt><a href="grails-app/controllers/grails/plugin/jcaptcha/JcaptchaController.html#wav" title="Property in JcaptchaController"><b>wav</b></a> - Property in <a href="grails-app/controllers/grails/plugin/jcaptcha/JcaptchaController.html">JcaptchaController</a>
</dt><dd></dd>
<dt><a href="grails-app/taglib/grails/plugin/jcaptcha/JcaptchaTagLib.html#wav" title="Property in JcaptchaTagLib"><b>wav</b></a> - Property in <a href="grails-app/taglib/grails/plugin/jcaptcha/JcaptchaTagLib.html">JcaptchaTagLib</a>
</dt><dd> Insert an 'embed' tag with a WAV image challenge.
 
 </dd>
</dl>

    

<hr>
<a name="_X_"><!-- --></a><h2>
<b>X</b></h2>

<dl>

</dl>

    

<hr>
<a name="_Y_"><!-- --></a><h2>
<b>Y</b></h2>

<dl>

</dl>

    

<hr>
<a name="_Z_"><!-- --></a><h2>
<b>Z</b></h2>

<dl>

</dl>

    

<hr>
<a name="___"><!-- --></a><h2>
<b>_</b></h2>

<dl>

</dl>

    

<hr>
<p>Groovy Documentation</p>
<hr>

</body></html>
