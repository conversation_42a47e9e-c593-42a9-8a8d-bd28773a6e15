<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">


<html><head>
<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
<!-- Generated by groovydoc (1.6.7) on  -->
<title>API Help (jcaptcha)</title>
<link href="groovy.ico" type="image/x-icon" rel="shortcut icon">
<link href="groovy.ico" type="image/x-icon" rel="icon">
<meta name="date" content="2010-02-05">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">

<script type="text/javascript">
function windowTitle()
{
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="API Help (jcaptcha)";
    }
}
</script>
<noscript>
</noscript>

</head><body onload="windowTitle();" bgcolor="white">
<hr>

<!-- ========= START OF TOP NAVBAR ======= -->
<a name="navbar_top"><!-- --></a>
<table summary="" width="100%" border="0" cellpadding="1" cellspacing="0">
<tbody><tr>
<td colspan="2" class="NavBarCell1" bgcolor="#eeeeff">
<a name="navbar_top_firstrow"><!-- --></a>
<table summary="" border="0" cellpadding="0" cellspacing="3">
  <tbody><tr valign="top" align="center">
  <td class="NavBarCell1" bgcolor="#eeeeff">    <a href="overview-summary.html"><font class="NavBarFont1"><b>Overview</b></font></a>&nbsp;</td>
  <td class="NavBarCell1" bgcolor="#eeeeff">    <font class="NavBarFont1">Package</font>&nbsp;</td>
  <td class="NavBarCell1" bgcolor="#eeeeff">    <font class="NavBarFont1">Class</font>&nbsp;</td>
  <!--
  <td class="NavBarCell1" bgcolor="#eeeeff">    <a href="overview-tree.html"><font class="NavBarFont1"><b>Tree</b></font></a>&nbsp;</td>
  -->
  <td class="NavBarCell1" bgcolor="#eeeeff">    <a href="deprecated-list.html"><font class="NavBarFont1"><b>Deprecated</b></font></a>&nbsp;</td>
<!--
  <td class="NavBarCell1" bgcolor="#eeeeff">    <a href="index-all.html"><font class="NavBarFont1"><b>Index</b></font></a>&nbsp;</td>
  -->
  <td class="NavBarCell1Rev" bgcolor="#ffffff"> &nbsp;<font class="NavBarFont1Rev"><b>Help</b></font>&nbsp;</td>
  </tr>
</tbody></table>
</td>
<td rowspan="3" valign="top" align="right"><em>
    <b>Groovy Documentation</b>
</em>
</td>
</tr>

<tr>
<td class="NavBarCell2" bgcolor="white"><font size="-2">
&nbsp;PREV&nbsp;
&nbsp;NEXT</font></td>
<td class="NavBarCell2" bgcolor="white"><font size="-2">
  <a href="index.html?help-doc.html" target="_top"><b>FRAMES</b></a>  &nbsp;
&nbsp;<a href="help-doc.html" target="_top"><b>NO FRAMES</b></a>  &nbsp;
&nbsp;<script type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="allclasses-frame.html"><B>All Classes</B></A>');
  }
  //-->
</script>
<noscript>
  <A HREF="allclasses-frame.html"><B>All Classes</B></A>
</noscript>

</font></td>
</tr>
</tbody></table>
<!-- ========= END OF TOP NAVBAR ========= -->

<hr>
<center>
<h1>
How This API Document Is Organized</h1>
</center>
This API (Application Programming Interface) document has pages
corresponding to the items in the navigation bar, described as follows.<h3>
Overview</h3>
<blockquote>

<p>
The <a href="overview-summary.html">Overview</a>
page is the front page of this API document and provides a list of all
packages with a summary for each. This page can also contain an overall
description of the set of packages.</p></blockquote>
<h3>
Package</h3>
<blockquote>

<p>
Each package has a page that contains a list of its classes and
interfaces, with a summary for each. This page can contain four
categories:</p><ul>
<li>Interfaces (italic)</li><li>Classes</li><li>Enums</li><li>Exceptions</li><li>Errors</li><li>Annotation Types</li></ul>
</blockquote>
<h3>
Class/Interface</h3>
<blockquote>

<p>
Each class, interface, nested class and nested interface has its own
separate page. Each of these pages has three sections consisting of a
class/interface description, summary tables, and detailed member
descriptions:</p><ul>
<li>Class inheritance diagram</li><li>Direct Subclasses</li><li>All Known Subinterfaces</li><li>All Known Implementing Classes</li><li>Class/interface declaration</li><li>Class/interface description
<p>
</p></li><li>Nested Class Summary</li><li>Field Summary</li><li>Constructor Summary</li><li>Method Summary
<p>
</p></li><li>Field Detail</li><li>Constructor Detail</li><li>Method Detail</li></ul>
Each summary entry contains the first sentence from the detailed
description for that item. The summary entries are alphabetical, while
the detailed descriptions are in the order they appear in the source
code. This preserves the logical groupings established by the
programmer.</blockquote>

<h3>Annotation Type</h3>
<blockquote>

<p>
Each annotation type has its own separate page with the following sections:</p><ul>
<li>Annotation Type declaration</li><li>Annotation Type description</li><li>Required Element Summary</li><li>Optional Element Summary</li><li>Element Detail</li></ul>
</blockquote>

<h3>Enum</h3>
<blockquote>

<p>
Each enum has its own separate page with the following sections:</p><ul>
<li>Enum declaration</li><li>Enum description</li><li>Enum Constant Summary</li><li>Enum Constant Detail</li></ul>
</blockquote>
<h3>Tree (Class Hierarchy)</h3>
<blockquote>
There is a <a href="overview-tree-todo.html">Class Hierarchy</a>
page for all packages, plus a hierarchy for each package. Each
hierarchy page contains a list of classes and a list of interfaces. The
classes are organized by inheritance structure starting with <code>java.lang.Object</code>. The interfaces do not inherit from <code>java.lang.Object</code>.<ul>
<li>When viewing the Overview page, clicking on "Tree" displays the hierarchy for all packages.</li><li>When viewing a particular package, class or interface page, clicking "Tree" displays the hierarchy for only that package.</li></ul>
</blockquote>
<h3>Deprecated API</h3>
<blockquote>
The <a href="deprecated-list.html">Deprecated API</a>
page lists all of the parts of the API that have been deprecated. A deprecated API
is not recommended for use, generally due to improvements, and a
replacement API is usually given. Deprecated APIs may be removed in
future implementations.</blockquote>
<h3>
Index</h3>
<blockquote>
The <a href="index-all.html">Index</a> contains an alphabetic list of all classes, interfaces, constructors, methods, and fields.</blockquote>
<h3>
Prev/Next</h3>
These links take you to the next or previous class, interface, package, or related page.<h3>
Frames/No Frames</h3>
These links show and hide the HTML frames.  All pages are available with or without frames.
<p>
</p>
<!--
<h3>Serialized Form</h3>
Each serializable or externalizable class has a description of its
serialization fields and methods. This information is of interest to
re-implementors, not to developers using the API. While there is no
link in the navigation bar, you can get to this information by going to
any serialized class and clicking "Serialized Form" in the "See also"
section of the class description.
<p>
</p>
-->
<h3>Constant Field Values</h3>
The <a href="constant-values-todo.html">Constant Field Values</a> page lists the static final fields and their values.
<p>
<font size="-1">
<em>
This help file applies to API documentation generated using the standard doclet.</em>
</font>
<br>
</p><hr>

<p>Groovy Documentation</p>
<hr>

</body></html>