<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">

<html><head>
<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
<!-- Generated by groovydoc (1.6.7) on  -->
<title>Deprecated API (jcaptcha)</title>
<meta name="date" content="2010-02-05">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link href="groovy.ico" type="image/x-icon" rel="shortcut icon">
<link href="groovy.ico" type="image/x-icon" rel="icon">

<script type="text/javascript">
function windowTitle()
{
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="Deprecated API (jcaptcha)";
    }
}
</script>
<noscript>
</noscript>

</head><body onload="windowTitle();" bgcolor="white">
<hr>

<!-- ========= START OF TOP NAVBAR ======= -->
<a name="navbar_top"><!-- --></a>
<table summary="" width="100%" border="0" cellpadding="1" cellspacing="0">
<tbody><tr>
<td colspan="2" class="NavBarCell1" bgcolor="#eeeeff">
<a name="navbar_top_firstrow"><!-- --></a>
<table summary="" border="0" cellpadding="0" cellspacing="3">
  <tbody><tr valign="top" align="center">
  <td class="NavBarCell1" bgcolor="#eeeeff">    <a href="overview-summary.html"><font class="NavBarFont1"><b>Overview</b></font></a>&nbsp;</td>
  <td class="NavBarCell1" bgcolor="#eeeeff">    <font class="NavBarFont1">Package</font>&nbsp;</td>
  <td class="NavBarCell1" bgcolor="#eeeeff">    <font class="NavBarFont1">Class</font>&nbsp;</td>
  <!--
  <td class="NavBarCell1" bgcolor="#eeeeff">    <a href="overview-tree.html"><font class="NavBarFont1"><b>Tree</b></font></a>&nbsp;</td>
  -->
  <td class="NavBarCell1Rev" bgcolor="#ffffff"> &nbsp;<font class="NavBarFont1Rev"><b>Deprecated</b></font>&nbsp;</td>
  <td class="NavBarCell1" bgcolor="#eeeeff">    <a href="index-all.html"><font class="NavBarFont1"><b>Index</b></font></a>&nbsp;</td>
  <td class="NavBarCell1" bgcolor="#eeeeff">    <a href="help-doc.html"><font class="NavBarFont1"><b>Help</b></font></a>&nbsp;</td>
  </tr>
</tbody></table>
</td>
<td rowspan="3" valign="top" align="right"><em>
    <b>Groovy Documentation</b>
</em>
</td>
</tr>

<tr>
<td class="NavBarCell2" bgcolor="white"><font size="-2">
&nbsp;PREV&nbsp;
&nbsp;NEXT</font></td>
<td class="NavBarCell2" bgcolor="white"><font size="-2">
  <a href="index.html?deprecated-list.html" target="_top"><b>FRAMES</b></a>  &nbsp;
&nbsp;<a href="deprecated-list.html" target="_top"><b>NO FRAMES</b></a>  &nbsp;
&nbsp;<script type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="allclasses-frame.html"><B>All Classes</B></A>');
  }
  //-->
</script>
<noscript>
  <A HREF="allclasses-frame.html"><B>All Classes</B></A>
</noscript>

</font></td>
</tr>
</tbody></table>
<!-- ========= END OF TOP NAVBAR ========= -->

<hr>
<center>
<h1>Deprecated API</h1>
</center>
<hr>













<hr>
<p>Groovy Documentation</p>
<hr>

</body></html>
