<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- **************************************************************** -->
<!-- *  PLEASE KEEP COMPLICATED EXPRESSIONS OUT OF THESE TEMPLATES, * -->
<!-- *  i.e. only iterate & print data where possible. Thanks, Jez. * -->
<!-- **************************************************************** -->

<html>
<head>
<!-- Generated by groovydoc (1.6.7) on Fri Feb 05 00:37:48 GMT 2010 -->
<title><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>roller (Groovy Documentation)</title>
<meta name="date" content="2010-02-05">
<link href="../../../../../groovy.ico" type="image/x-icon" rel="shortcut icon">
<link href="../../../../../groovy.ico" type="image/x-icon" rel="icon">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<script type="text/javascript">
function windowTitle()
{
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="JcaptchaController (Groovy Documentation)";
    }
}
</script>
<noscript>
</noscript>

</head><body onload="windowTitle();" bgcolor="white">
<hr>

<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>

  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <!--<TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  -->
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../../../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
<b>Groovy Documentation</b>
</EM></TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><!--<FONT SIZE="-2">
&nbsp;<A HREF="../../../../../groovy/lang/ExpandoMetaClass.ExpandoMetaProperty.html" title="class in groovy.lang"><B>PREV CLASS</B></A>&nbsp;

&nbsp;<A HREF="../../../../../groovy/lang/GroovyClassLoader.html" title="class in groovy.lang"><B>NEXT CLASS</B></A></FONT>--></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../../../../index.html?grails-app/controllers/grails/plugin/jcaptcha/JcaptchaController.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="JcaptchaController.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<script type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../../../../allclasses-frame.html"><B>All Classes</B></A>');
  }
  //-->
</script>
<noscript>
  <A HREF="../../../../../allclasses-frame.html"><B>All Classes</B></A>
</noscript>

</FONT></TD>

</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#property_summary">PROPERTY</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;METHOD</FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#prop_detail">PROPERTY</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;METHOD</FONT></TD>
</TR>
</TABLE>
<!-- ========= END OF TOP NAVBAR ========= -->


<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
grails-app.controllers.grails.plugin.jcaptcha</FONT>
<BR>
<FONT CLASS="ClassTitleFont">Class JcaptchaController</FONT></H2>
<pre>java.lang.Object
  <img src='../../../../../inherit.gif'>grails-app.controllers.grails.plugin.jcaptcha.JcaptchaController
</pre><hr>
<PRE>class JcaptchaController

</PRE>

<P>
 Exposes actions that 'render' captcha challenges.
 
 The 'id' used to identify the challenge is session.id. Therefore you
 need to use session.id when validating a response.
 
 You typically won't need to use this class directly, take a look at the
 JcaptchaTabLib.
 
 <DL><DT><B>author:</B></DT><DD>LD <<EMAIL>>
 </DD></DL>
</P>
<hr>


<!-- =========== NESTED CLASS SUMMARY =========== -->

<A NAME="nested_summary"><!-- --></A>


<!-- =========== ENUM CONSTANT SUMMARY =========== -->

<A NAME="enum_constant_summary"><!-- --></A>


<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>


<!-- =========== PROPERTY SUMMARY =========== -->

<A NAME="property_summary"><!-- --></A>

    <TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
    <TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
    <TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
    <B>Property Summary</B></FONT></TH>
    </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
        <CODE>def</CODE></FONT></TD>
        <TD>
            <CODE><B><A HREF="#afterInterceptor">afterInterceptor</A></B></CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
        <CODE>def</CODE></FONT></TD>
        <TD>
            <CODE><B><A HREF="#beforeInterceptor">beforeInterceptor</A></B></CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
        <CODE>def</CODE></FONT></TD>
        <TD>
            <CODE><B><A HREF="#captcha">captcha</A></B></CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
        <CODE>def</CODE></FONT></TD>
        <TD>
            <CODE><B><A HREF="#captchaName">captchaName</A></B></CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
        <CODE>def</CODE></FONT></TD>
        <TD>
            <CODE><B><A HREF="#challenge">challenge</A></B></CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
        <CODE>def</CODE></FONT></TD>
        <TD>
            <CODE><B><A HREF="#data">data</A></B></CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
        <CODE>def</CODE></FONT></TD>
        <TD>
            <CODE><B><A HREF="#jcaptchaService">jcaptchaService</A></B></CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
        <CODE>def</CODE></FONT></TD>
        <TD>
            <CODE><B><A HREF="#jpeg">jpeg</A></B></CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
        <CODE>def</CODE></FONT></TD>
        <TD>
            <CODE><B><A HREF="#wav">wav</A></B></CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
    </TABLE>
    &nbsp;


<!-- =========== ELEMENT SUMMARY =========== -->



<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>

    <TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
    <TR BGCOLOR="#D5D5FF" CLASS="TableHeadingColor">
    <TD COLSPAN=2><FONT SIZE="+2">
    <B>Constructor Summary</B></FONT></TD>
    </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD>
            <CODE><B><a href="#JcaptchaController()">JcaptchaController</a></B>()</CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
    </TABLE>
    &nbsp;


<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<table BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
                    <TR CLASS="TableHeadingColor">
                    <TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2" CLASS="ClassHeadingFont">
                    <B>Method Summary</B></FONT></TH>
                    </TR>
                    </table>
                    &nbsp;<table BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
                <tr CLASS="TableSubHeadingColor"><th ALIGN="left" COLSPAN="2">
                <b>Methods inherited from class java.lang.Object</b>
                </th></tr>
                <tr class="TableRowColor"><td colspan='2'>wait, wait, wait, hashCode, getClass, equals, toString, notify, notifyAll</td></tr>
                </table>
                &nbsp;

<P>

<!-- ============ ENUM CONSTANT DETAIL ========== -->

<A NAME="enum_constant_detail"><!-- --></A>


<!-- =========== FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>


<!-- =========== PROPERTY DETAIL =========== -->

<A NAME="prop_detail"><!-- --></A>

    <TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
    <TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
    <TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
    <B>Property Detail</B></FONT></TH>
    </TR>
    </TABLE>
    
        <A NAME="afterInterceptor"><!-- --></A><H3>afterInterceptor</H3>
        <PRE>def <B>afterInterceptor</B></PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
        <A NAME="beforeInterceptor"><!-- --></A><H3>beforeInterceptor</H3>
        <PRE>def <B>beforeInterceptor</B></PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
        <A NAME="captcha"><!-- --></A><H3>captcha</H3>
        <PRE>def <B>captcha</B></PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
        <A NAME="captchaName"><!-- --></A><H3>captchaName</H3>
        <PRE>def <B>captchaName</B></PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
        <A NAME="challenge"><!-- --></A><H3>challenge</H3>
        <PRE>def <B>challenge</B></PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
        <A NAME="data"><!-- --></A><H3>data</H3>
        <PRE>def <B>data</B></PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
        <A NAME="jcaptchaService"><!-- --></A><H3>jcaptchaService</H3>
        <PRE>def <B>jcaptchaService</B></PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
        <A NAME="jpeg"><!-- --></A><H3>jpeg</H3>
        <PRE>def <B>jpeg</B></PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
        <A NAME="wav"><!-- --></A><H3>wav</H3>
        <PRE>def <B>wav</B></PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
    &nbsp;


<!-- =========== ELEMENT DETAIL =========== -->

<A NAME="element_detail"><!-- --></A>


<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>

    <TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
    <TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
    <TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
    <B>Constructor Detail</B></FONT></TH>
    </TR>
    </TABLE>
    
        <A NAME="JcaptchaController()"><!-- --></A><H3>
        JcaptchaController</H3>
        <PRE><B>JcaptchaController</B>()</PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
    &nbsp;


<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>


<!-- ========= END OF CLASS DATA ========= -->
<p>Groovy Documentation</p>
<hr>

</body>
</html>
