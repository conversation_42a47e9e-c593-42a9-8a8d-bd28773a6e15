<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- **************************************************************** -->
<!-- *  PLEASE KEEP COMPLICATED EXPRESSIONS OUT OF THESE TEMPLATES, * -->
<!-- *  i.e. only iterate & print data where possible. Thanks, Jez. * -->
<!-- **************************************************************** -->

<html>
<head>
<!-- Generated by groovydoc (1.6.7) on Wed Feb 03 00:02:49 GMT 2010 -->
<title>JcaptchaService (Groovy Documentation)</title>
<meta name="date" content="2010-02-03">
<link href="../../groovy.ico" type="image/x-icon" rel="shortcut icon">
<link href="../../groovy.ico" type="image/x-icon" rel="icon">
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript">
function windowTitle()
{
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="JcaptchaService (Groovy Documentation)";
    }
}
</script>
<noscript>
</noscript>

</head><body onload="windowTitle();" bgcolor="white">
<hr>

<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>

  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <!--<TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  -->
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
<b>Groovy Documentation</b>
</EM></TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><!--<FONT SIZE="-2">
&nbsp;<A HREF="../../groovy/lang/ExpandoMetaClass.ExpandoMetaProperty.html" title="class in groovy.lang"><B>PREV CLASS</B></A>&nbsp;

&nbsp;<A HREF="../../groovy/lang/GroovyClassLoader.html" title="class in groovy.lang"><B>NEXT CLASS</B></A></FONT>--></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../../index.html?grails-app/services/JcaptchaService.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="JcaptchaService.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<script type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../../allclasses-frame.html"><B>All Classes</B></A>');
  }
  //-->
</script>
<noscript>
  <A HREF="../../allclasses-frame.html"><B>All Classes</B></A>
</noscript>

</FONT></TD>

</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#property_summary">PROPERTY</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_summary">METHOD</A></FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#prop_detail">PROPERTY</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;<A HREF="#method_detail">METHOD</A></FONT></TD>
</TR>
</TABLE>
<!-- ========= END OF TOP NAVBAR ========= -->


<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
grails-app.services</FONT>
<BR>
<FONT CLASS="ClassTitleFont">Class JcaptchaService</FONT></H2>
<pre>java.lang.Object
  <img src='../../inherit.gif'>grails-app.services.JcaptchaService
</pre><hr>
<PRE>class JcaptchaService

</PRE>

<P>
 Provides access to the captchas as well as provides some util
 type methods to convert captchas to usable data.
 
 <DL><DT><B>author:</B></DT><DD>LD <<EMAIL>>
 </DD></DL>
</P>
<hr>


<!-- =========== NESTED CLASS SUMMARY =========== -->

<A NAME="nested_summary"><!-- --></A>


<!-- =========== ENUM CONSTANT SUMMARY =========== -->

<A NAME="enum_constant_summary"><!-- --></A>


<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>


<!-- =========== PROPERTY SUMMARY =========== -->

<A NAME="property_summary"><!-- --></A>

    <TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
    <TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
    <TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
    <B>Property Summary</B></FONT></TH>
    </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
        <CODE>def</CODE></FONT></TD>
        <TD>
            <CODE><B><A HREF="#grailsApplication">grailsApplication</A></B></CODE>
            <BR>
            <P> Used to access the captchas defined as part of the app config.
	 </P>
        </TD>
        </TR>
    
    </TABLE>
    &nbsp;


<!-- =========== ELEMENT SUMMARY =========== -->



<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>

    <TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
    <TR BGCOLOR="#D5D5FF" CLASS="TableHeadingColor">
    <TD COLSPAN=2><FONT SIZE="+2">
    <B>Constructor Summary</B></FONT></TD>
    </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD>
            <CODE><B><a href="#JcaptchaService()">JcaptchaService</a></B>()</CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
    </TABLE>
    &nbsp;


<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>

    <TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
    <TR CLASS="TableHeadingColor">
    <TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2" CLASS="ClassHeadingFont">
    <B>Method Summary</B></FONT></TH>
    </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1" CLASS="ClassItemFont">
            <CODE>byte[]</CODE></FONT>
        </TD>
        <TD>
            <CODE><b><a href="#challengeAsJpeg(BufferedImage)">challengeAsJpeg</a></b>(BufferedImage challenge)</CODE>
            <BR>
            <P> Utility routine to turn an image challenge into a JPEG stream.
 
 </P>
        </TD>
        </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1" CLASS="ClassItemFont">
            <CODE>byte[]</CODE></FONT>
        </TD>
        <TD>
            <CODE><b><a href="#challengeAsWav(AudioInputStream)">challengeAsWav</a></b>(AudioInputStream challenge)</CODE>
            <BR>
            <P> Utility routine to turn a sound challenge into a WAV stream.
 
 </P>
        </TD>
        </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1" CLASS="ClassItemFont">
            <CODE>CaptchaService</CODE></FONT>
        </TD>
        <TD>
            <CODE><b><a href="#getCaptchaService(String)">getCaptchaService</a></b>(java.lang.String captchaName)</CODE>
            <BR>
            <P> Retrieves a captcha by name.
 
 </P>
        </TD>
        </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1" CLASS="ClassItemFont">
            <CODE>boolean</CODE></FONT>
        </TD>
        <TD>
            <CODE><b><a href="#validateResponse(def, def, def)">validateResponse</a></b>(def captchaName, def id, def response)</CODE>
            <BR>
            <P> Used to verify the response to a challenge.
 
 </P>
        </TD>
        </TR>
    
    </TABLE>
    &nbsp;
<table BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
                <tr CLASS="TableSubHeadingColor"><th ALIGN="left" COLSPAN="2">
                <b>Methods inherited from class java.lang.Object</b>
                </th></tr>
                <tr class="TableRowColor"><td colspan='2'>wait, wait, wait, hashCode, getClass, equals, toString, notify, notifyAll</td></tr>
                </table>
                &nbsp;

<P>

<!-- ============ ENUM CONSTANT DETAIL ========== -->

<A NAME="enum_constant_detail"><!-- --></A>


<!-- =========== FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>


<!-- =========== PROPERTY DETAIL =========== -->

<A NAME="prop_detail"><!-- --></A>

    <TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
    <TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
    <TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
    <B>Property Detail</B></FONT></TH>
    </TR>
    </TABLE>
    
        <A NAME="grailsApplication"><!-- --></A><H3>grailsApplication</H3>
        <PRE>def <B>grailsApplication</B></PRE>
        <DL>
        <DD> Used to access the captchas defined as part of the app config.
	 
        </DD>
        <P>
        </DL>
        <HR>
    
    &nbsp;


<!-- =========== ELEMENT DETAIL =========== -->

<A NAME="element_detail"><!-- --></A>


<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>

    <TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
    <TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
    <TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
    <B>Constructor Detail</B></FONT></TH>
    </TR>
    </TABLE>
    
        <A NAME="JcaptchaService()"><!-- --></A><H3>
        JcaptchaService</H3>
        <PRE><B>JcaptchaService</B>()</PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
    &nbsp;


<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>

    <TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
    <TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
    <TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
    <B>Method Detail</B></FONT></TH>
    </TR>
    </TABLE>
    
        <A NAME="challengeAsJpeg(BufferedImage)"><!-- --></A><H3>
        challengeAsJpeg</H3>
        <PRE>byte[] <B>challengeAsJpeg</B>(BufferedImage challenge)</PRE>
        <DL>
        <DD> Utility routine to turn an image challenge into a JPEG stream.
 
 <DL><DT><B>param:</B></DT><DD>challenge The image data
 </DD></DL><DL><DT><B>return:</B></DT><DD>A raw bunch of bytes which come together to be a JPEG.
	 </DD></DL>
        </DD>
        <P>
        </DL>
        <HR>
    
        <A NAME="challengeAsWav(AudioInputStream)"><!-- --></A><H3>
        challengeAsWav</H3>
        <PRE>byte[] <B>challengeAsWav</B>(AudioInputStream challenge)</PRE>
        <DL>
        <DD> Utility routine to turn a sound challenge into a WAV stream.
 
 <DL><DT><B>param:</B></DT><DD>challenge The sound data
 </DD></DL><DL><DT><B>return:</B></DT><DD>A raw bunch of bytes which come together to be a WAV.
	 </DD></DL>
        </DD>
        <P>
        </DL>
        <HR>
    
        <A NAME="getCaptchaService(String)"><!-- --></A><H3>
        getCaptchaService</H3>
        <PRE>CaptchaService <B>getCaptchaService</B>(java.lang.String captchaName)</PRE>
        <DL>
        <DD> Retrieves a captcha by name.
 
 <DL><DT><B>param:</B></DT><DD>captchaName The 'key' of the captcha defined in config.
 </DD></DL><DL><DT><B>throws:</B></DT><DD>IllegalArgumentException If captchaName is null.
 </DD></DL><DL><DT><B>throws:</B></DT><DD>IllegalStateException If there is no captcha by that name.
 </DD></DL><DL><DT><B>returns:</B></DT><DD>The captcha service keyed by 'captchaName'
	 </DD></DL>
        </DD>
        <P>
        </DL>
        <HR>
    
        <A NAME="validateResponse(def, def, def)"><!-- --></A><H3>
        validateResponse</H3>
        <PRE>boolean <B>validateResponse</B>(def captchaName, def id, def response)</PRE>
        <DL>
        <DD> Used to verify the response to a challenge.
 
 <DL><DT><B>param:</B></DT><DD>captchaName The key of the captcha
 </DD></DL><DL><DT><B>param:</B></DT><DD>id The identifier used when retrieving the challenge (often session.id)
 </DD></DL><DL><DT><B>param:</B></DT><DD>response What the user 'entered' to meet the challenge
 </DD></DL><DL><DT><B>return:</B></DT><DD>True if the response meets the challenge
 </DD></DL><DL><DT><B>see:</B></DT><DD>getCaptchaService()</DD></DL>
        </DD>
        <P>
        </DL>
        <HR>
    
    &nbsp;


<!-- ========= END OF CLASS DATA ========= -->
<p>Groovy Documentation</p>
<hr>

</body>
</html>
