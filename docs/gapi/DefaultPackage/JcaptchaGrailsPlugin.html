<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- **************************************************************** -->
<!-- *  PLEASE KEEP COMPLICATED EXPRESSIONS OUT OF THESE TEMPLATES, * -->
<!-- *  i.e. only iterate & print data where possible. Thanks, Jez. * -->
<!-- **************************************************************** -->

<html>
<head>
<!-- Generated by groovydoc (1.6.7) on Fri Feb 05 00:37:48 GMT 2010 -->
<title>JcaptchaGrailsPlugin (Groovy Documentation)</title>
<meta name="date" content="2010-02-05">
<link href="../groovy.ico" type="image/x-icon" rel="shortcut icon">
<link href="../groovy.ico" type="image/x-icon" rel="icon">
<link rel="stylesheet" type="text/css" href="../stylesheet.css" title="Style">
<script type="text/javascript">
function windowTitle()
{
    if (location.href.indexOf('is-external=true') == -1) {
        parent.document.title="JcaptchaGrailsPlugin (Groovy Documentation)";
    }
}
</script>
<noscript>
</noscript>

</head><body onload="windowTitle();" bgcolor="white">
<hr>

<!-- ========= START OF TOP NAVBAR ======= -->
<A NAME="navbar_top"><!-- --></A>
<TABLE BORDER="0" WIDTH="100%" CELLPADDING="1" CELLSPACING="0" SUMMARY="">
<TR>
<TD COLSPAN=2 BGCOLOR="#EEEEFF" CLASS="NavBarCell1">
<A NAME="navbar_top_firstrow"><!-- --></A>
<TABLE BORDER="0" CELLPADDING="0" CELLSPACING="3" SUMMARY="">
  <TR ALIGN="center" VALIGN="top">
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../overview-summary.html"><FONT CLASS="NavBarFont1"><B>Overview</B></FONT></A>&nbsp;</TD>

  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-summary.html"><FONT CLASS="NavBarFont1"><B>Package</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#FFFFFF" CLASS="NavBarCell1Rev"> &nbsp;<FONT CLASS="NavBarFont1Rev"><B>Class</B></FONT>&nbsp;</TD>
  <!--<TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="package-tree.html"><FONT CLASS="NavBarFont1"><B>Tree</B></FONT></A>&nbsp;</TD>
  -->
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../deprecated-list.html"><FONT CLASS="NavBarFont1"><B>Deprecated</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../index-all.html"><FONT CLASS="NavBarFont1"><B>Index</B></FONT></A>&nbsp;</TD>
  <TD BGCOLOR="#EEEEFF" CLASS="NavBarCell1">    <A HREF="../help-doc.html"><FONT CLASS="NavBarFont1"><B>Help</B></FONT></A>&nbsp;</TD>
  </TR>
</TABLE>
</TD>
<TD ALIGN="right" VALIGN="top" ROWSPAN=3><EM>
<b>Groovy Documentation</b>
</EM></TD>
</TR>

<TR>
<TD BGCOLOR="white" CLASS="NavBarCell2"><!--<FONT SIZE="-2">
&nbsp;<A HREF="../groovy/lang/ExpandoMetaClass.ExpandoMetaProperty.html" title="class in groovy.lang"><B>PREV CLASS</B></A>&nbsp;

&nbsp;<A HREF="../groovy/lang/GroovyClassLoader.html" title="class in groovy.lang"><B>NEXT CLASS</B></A></FONT>--></TD>
<TD BGCOLOR="white" CLASS="NavBarCell2"><FONT SIZE="-2">
  <A HREF="../index.html?DefaultPackage/JcaptchaGrailsPlugin.html" target="_top"><B>FRAMES</B></A>  &nbsp;
&nbsp;<A HREF="JcaptchaGrailsPlugin.html" target="_top"><B>NO FRAMES</B></A>  &nbsp;
&nbsp;<script type="text/javascript">
  <!--
  if(window==top) {
    document.writeln('<A HREF="../allclasses-frame.html"><B>All Classes</B></A>');
  }
  //-->
</script>
<noscript>
  <A HREF="../allclasses-frame.html"><B>All Classes</B></A>
</noscript>

</FONT></TD>

</TR>
<TR>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
SUMMARY:&nbsp;NESTED&nbsp;|&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#property_summary">PROPERTY</A>&nbsp;|&nbsp;<A HREF="#constructor_summary">CONSTR</A>&nbsp;|&nbsp;METHOD</FONT></TD>
<TD VALIGN="top" CLASS="NavBarCell3"><FONT SIZE="-2">
DETAIL:&nbsp;FIELD&nbsp;|&nbsp;<A HREF="#prop_detail">PROPERTY</A>&nbsp;|&nbsp;<A HREF="#constructor_detail">CONSTR</A>&nbsp;|&nbsp;METHOD</FONT></TD>
</TR>
</TABLE>
<!-- ========= END OF TOP NAVBAR ========= -->


<HR>
<!-- ======== START OF CLASS DATA ======== -->
<H2>
<FONT SIZE="-1">
DefaultPackage</FONT>
<BR>
<FONT CLASS="ClassTitleFont">Class JcaptchaGrailsPlugin</FONT></H2>
<pre>java.lang.Object
  <img src='../inherit.gif'>DefaultPackage.JcaptchaGrailsPlugin
</pre><hr>
<PRE>class JcaptchaGrailsPlugin

</PRE>

<P>
 Simplest. Plugin. Class. Ever.
 
 <DL><DT><B>author:</B></DT><DD>LD <<EMAIL>>
 </DD></DL>
</P>
<hr>


<!-- =========== NESTED CLASS SUMMARY =========== -->

<A NAME="nested_summary"><!-- --></A>


<!-- =========== ENUM CONSTANT SUMMARY =========== -->

<A NAME="enum_constant_summary"><!-- --></A>


<!-- =========== FIELD SUMMARY =========== -->

<A NAME="field_summary"><!-- --></A>


<!-- =========== PROPERTY SUMMARY =========== -->

<A NAME="property_summary"><!-- --></A>

    <TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
    <TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
    <TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
    <B>Property Summary</B></FONT></TH>
    </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
        <CODE>def</CODE></FONT></TD>
        <TD>
            <CODE><B><A HREF="#author">author</A></B></CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
        <CODE>def</CODE></FONT></TD>
        <TD>
            <CODE><B><A HREF="#authorEmail">authorEmail</A></B></CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
        <CODE>def</CODE></FONT></TD>
        <TD>
            <CODE><B><A HREF="#dependsOn">dependsOn</A></B></CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
        <CODE>def</CODE></FONT></TD>
        <TD>
            <CODE><B><A HREF="#description">description</A></B></CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
        <CODE>def</CODE></FONT></TD>
        <TD>
            <CODE><B><A HREF="#doWithApplicationContext">doWithApplicationContext</A></B></CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
        <CODE>def</CODE></FONT></TD>
        <TD>
            <CODE><B><A HREF="#doWithDynamicMethods">doWithDynamicMethods</A></B></CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
        <CODE>def</CODE></FONT></TD>
        <TD>
            <CODE><B><A HREF="#doWithSpring">doWithSpring</A></B></CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
        <CODE>def</CODE></FONT></TD>
        <TD>
            <CODE><B><A HREF="#doWithWebDescriptor">doWithWebDescriptor</A></B></CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
        <CODE>def</CODE></FONT></TD>
        <TD>
            <CODE><B><A HREF="#documentation">documentation</A></B></CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
        <CODE>def</CODE></FONT></TD>
        <TD>
            <CODE><B><A HREF="#onApplicationChange">onApplicationChange</A></B></CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
        <CODE>def</CODE></FONT></TD>
        <TD>
            <CODE><B><A HREF="#onChange">onChange</A></B></CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
        <CODE>def</CODE></FONT></TD>
        <TD>
            <CODE><B><A HREF="#title">title</A></B></CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD ALIGN="right" VALIGN="top" WIDTH="1%"><FONT SIZE="-1">
        <CODE>def</CODE></FONT></TD>
        <TD>
            <CODE><B><A HREF="#version">version</A></B></CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
    </TABLE>
    &nbsp;


<!-- =========== ELEMENT SUMMARY =========== -->



<!-- ======== CONSTRUCTOR SUMMARY ======== -->

<A NAME="constructor_summary"><!-- --></A>

    <TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
    <TR BGCOLOR="#D5D5FF" CLASS="TableHeadingColor">
    <TD COLSPAN=2><FONT SIZE="+2">
    <B>Constructor Summary</B></FONT></TD>
    </TR>
    
        <TR BGCOLOR="white" CLASS="TableRowColor">
        <TD>
            <CODE><B><a href="#JcaptchaGrailsPlugin()">JcaptchaGrailsPlugin</a></B>()</CODE>
            <BR>
            <P></P>
        </TD>
        </TR>
    
    </TABLE>
    &nbsp;


<!-- ========== METHOD SUMMARY =========== -->

<A NAME="method_summary"><!-- --></A>
<table BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
                    <TR CLASS="TableHeadingColor">
                    <TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2" CLASS="ClassHeadingFont">
                    <B>Method Summary</B></FONT></TH>
                    </TR>
                    </table>
                    &nbsp;<table BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
                <tr CLASS="TableSubHeadingColor"><th ALIGN="left" COLSPAN="2">
                <b>Methods inherited from class java.lang.Object</b>
                </th></tr>
                <tr class="TableRowColor"><td colspan='2'>wait, wait, wait, hashCode, getClass, equals, toString, notify, notifyAll</td></tr>
                </table>
                &nbsp;

<P>

<!-- ============ ENUM CONSTANT DETAIL ========== -->

<A NAME="enum_constant_detail"><!-- --></A>


<!-- =========== FIELD DETAIL =========== -->

<A NAME="field_detail"><!-- --></A>


<!-- =========== PROPERTY DETAIL =========== -->

<A NAME="prop_detail"><!-- --></A>

    <TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
    <TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
    <TH ALIGN="left" COLSPAN="2"><FONT SIZE="+2">
    <B>Property Detail</B></FONT></TH>
    </TR>
    </TABLE>
    
        <A NAME="author"><!-- --></A><H3>author</H3>
        <PRE>def <B>author</B></PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
        <A NAME="authorEmail"><!-- --></A><H3>authorEmail</H3>
        <PRE>def <B>authorEmail</B></PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
        <A NAME="dependsOn"><!-- --></A><H3>dependsOn</H3>
        <PRE>def <B>dependsOn</B></PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
        <A NAME="description"><!-- --></A><H3>description</H3>
        <PRE>def <B>description</B></PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
        <A NAME="doWithApplicationContext"><!-- --></A><H3>doWithApplicationContext</H3>
        <PRE>def <B>doWithApplicationContext</B></PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
        <A NAME="doWithDynamicMethods"><!-- --></A><H3>doWithDynamicMethods</H3>
        <PRE>def <B>doWithDynamicMethods</B></PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
        <A NAME="doWithSpring"><!-- --></A><H3>doWithSpring</H3>
        <PRE>def <B>doWithSpring</B></PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
        <A NAME="doWithWebDescriptor"><!-- --></A><H3>doWithWebDescriptor</H3>
        <PRE>def <B>doWithWebDescriptor</B></PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
        <A NAME="documentation"><!-- --></A><H3>documentation</H3>
        <PRE>def <B>documentation</B></PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
        <A NAME="onApplicationChange"><!-- --></A><H3>onApplicationChange</H3>
        <PRE>def <B>onApplicationChange</B></PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
        <A NAME="onChange"><!-- --></A><H3>onChange</H3>
        <PRE>def <B>onChange</B></PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
        <A NAME="title"><!-- --></A><H3>title</H3>
        <PRE>def <B>title</B></PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
        <A NAME="version"><!-- --></A><H3>version</H3>
        <PRE>def <B>version</B></PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
    &nbsp;


<!-- =========== ELEMENT DETAIL =========== -->

<A NAME="element_detail"><!-- --></A>


<!-- ========= CONSTRUCTOR DETAIL ======== -->

<A NAME="constructor_detail"><!-- --></A>

    <TABLE BORDER="1" WIDTH="100%" CELLPADDING="3" CELLSPACING="0" SUMMARY="">
    <TR BGCOLOR="#CCCCFF" CLASS="TableHeadingColor">
    <TH ALIGN="left" COLSPAN="1"><FONT SIZE="+2">
    <B>Constructor Detail</B></FONT></TH>
    </TR>
    </TABLE>
    
        <A NAME="JcaptchaGrailsPlugin()"><!-- --></A><H3>
        JcaptchaGrailsPlugin</H3>
        <PRE><B>JcaptchaGrailsPlugin</B>()</PRE>
        <DL>
        <DD>
        </DD>
        <P>
        </DL>
        <HR>
    
    &nbsp;


<!-- ============ METHOD DETAIL ========== -->

<A NAME="method_detail"><!-- --></A>


<!-- ========= END OF CLASS DATA ========= -->
<p>Groovy Documentation</p>
<hr>

</body>
</html>
